import crypto from 'crypto';
import { Response, Request, NextFunction } from 'express';
import { RedisClientType } from 'redis';
import { 
  ApiResponse, 
  PaginationInfo, 
  CustomError,
  AuthenticatedRequest 
} from './types';

// Generate unique identifiers
export const generateSaleNumber = (): string => {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(4).toString('hex');
  return `SALE-${timestamp}-${random}`.toUpperCase();
};

export const generateOrderNumber = (): string => {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(4).toString('hex');
  return `ORD-${timestamp}-${random}`.toUpperCase();
};

export const generateSKU = (categoryName: string, productName: string): string => {
  const catCode = categoryName.substring(0, 3).toUpperCase();
  const prodCode = productName.substring(0, 3).toUpperCase();
  const random = crypto.randomBytes(2).toString('hex').toUpperCase();
  return `${catCode}-${prodCode}-${random}`;
};

export const generateId = (): string => {
  return crypto.randomUUID();
};

// Date utilities
export const formatDate = (date: Date | string): string => {
  return new Date(date).toISOString().split('T')[0];
};

export const formatDateTime = (date: Date | string): string => {
  return new Date(date).toISOString();
};

export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export const isToday = (date: Date | string): boolean => {
  const today = new Date();
  const checkDate = new Date(date);
  return today.toDateString() === checkDate.toDateString();
};

export const startOfDay = (date: Date = new Date()): Date => {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
};

export const endOfDay = (date: Date = new Date()): Date => {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);
};

// Number utilities
export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

export const calculateTax = (amount: number, taxRate: number = 0.1): number => {
  return Math.round(amount * taxRate * 100) / 100;
};

export const calculateDiscount = (amount: number, discountPercent: number): number => {
  return Math.round(amount * (discountPercent / 100) * 100) / 100;
};

export const roundToDecimals = (value: number, decimals: number = 2): number => {
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
};

// String utilities
export const slugify = (text: string): string => {
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
};

export const capitalize = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

export const truncate = (text: string, length: number = 100): string => {
  if (text.length <= length) return text;
  return text.substring(0, length) + '...';
};

export const sanitizeString = (str: string): string => {
  return str.replace(/[<>]/g, '');
};

// Array utilities
export const paginate = <T>(
  array: T[], 
  page: number = 1, 
  limit: number = 10
): { data: T[]; pagination: PaginationInfo } => {
  const offset = (page - 1) * limit;
  const paginatedItems = array.slice(offset, offset + limit);
  
  return {
    data: paginatedItems,
    pagination: {
      page,
      limit,
      total: array.length,
      totalPages: Math.ceil(array.length / limit),
      hasNext: offset + limit < array.length,
      hasPrev: page > 1,
    },
  };
};

export const groupBy = <T>(array: T[], key: keyof T): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
};

export const unique = <T>(array: T[]): T[] => {
  return [...new Set(array)];
};

export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

// Object utilities
export const pick = <T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (obj[key] !== undefined) {
      result[key] = obj[key];
    }
  });
  return result;
};

export const omit = <T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
  const result = { ...obj };
  keys.forEach(key => delete result[key]);
  return result;
};

export const isEmpty = (obj: any): boolean => {
  if (obj == null) return true;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
};

// Error handling utilities
export const createError = (
  message: string, 
  statusCode: number = 500, 
  code?: string
): CustomError => {
  const error = new Error(message) as CustomError;
  error.statusCode = statusCode;
  error.code = code;
  return error;
};

export const handleAsyncError = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Response utilities
export const sendSuccess = <T>(
  res: Response,
  data: T = null as T,
  message: string = 'Success',
  statusCode: number = 200
): void => {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString(),
  };
  res.status(statusCode).json(response);
};

export const sendError = (
  res: Response,
  message: string = 'Internal Server Error',
  statusCode: number = 500,
  errors: any = null
): void => {
  const response: ApiResponse = {
    success: false,
    message,
    errors,
    timestamp: new Date().toISOString(),
  };
  res.status(statusCode).json(response);
};

export const sendPaginatedResponse = <T>(
  res: Response,
  data: T[],
  pagination: PaginationInfo,
  message: string = 'Success'
): void => {
  sendSuccess(res, { data, pagination }, message);
};

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
  return phoneRegex.test(phone);
};

export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

// Cache utilities
export const getCacheKey = (...parts: string[]): string => {
  return parts.join(':');
};

export const setCacheWithTTL = async (
  redisClient: RedisClientType,
  key: string,
  value: any,
  ttlSeconds: number = 3600
): Promise<void> => {
  await redisClient.setEx(key, ttlSeconds, JSON.stringify(value));
};

export const getCacheValue = async <T>(
  redisClient: RedisClientType,
  key: string
): Promise<T | null> => {
  const value = await redisClient.get(key);
  return value ? JSON.parse(value) : null;
};

export const deleteCacheKey = async (
  redisClient: RedisClientType,
  key: string
): Promise<void> => {
  await redisClient.del(key);
};

export const deleteCachePattern = async (
  redisClient: RedisClientType,
  pattern: string
): Promise<void> => {
  const keys = await redisClient.keys(pattern);
  if (keys.length > 0) {
    await redisClient.del(keys);
  }
};

// Async utilities
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const retry = async <T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (attempt === maxAttempts) break;
      await sleep(delay * attempt);
    }
  }
  
  throw lastError!;
};

// Security utilities
export const generateSecureToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

export const hashString = (str: string): string => {
  return crypto.createHash('sha256').update(str).digest('hex');
};

// Type guards
export const isString = (value: any): value is string => {
  return typeof value === 'string';
};

export const isNumber = (value: any): value is number => {
  return typeof value === 'number' && !isNaN(value);
};

export const isBoolean = (value: any): value is boolean => {
  return typeof value === 'boolean';
};

export const isObject = (value: any): value is object => {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
};

export const isArray = (value: any): value is any[] => {
  return Array.isArray(value);
};

export default {
  // ID generators
  generateSaleNumber,
  generateOrderNumber,
  generateSKU,
  generateId,
  
  // Date utilities
  formatDate,
  formatDateTime,
  addDays,
  isToday,
  startOfDay,
  endOfDay,
  
  // Number utilities
  formatCurrency,
  calculateTax,
  calculateDiscount,
  roundToDecimals,
  
  // String utilities
  slugify,
  capitalize,
  truncate,
  sanitizeString,
  
  // Array utilities
  paginate,
  groupBy,
  unique,
  chunk,
  
  // Object utilities
  pick,
  omit,
  isEmpty,
  
  // Error handling
  createError,
  handleAsyncError,
  
  // Response utilities
  sendSuccess,
  sendError,
  sendPaginatedResponse,
  
  // Validation utilities
  isValidEmail,
  isValidPhone,
  isValidURL,
  isValidUUID,
  
  // Cache utilities
  getCacheKey,
  setCacheWithTTL,
  getCacheValue,
  deleteCacheKey,
  deleteCachePattern,
  
  // Async utilities
  sleep,
  retry,
  
  // Security utilities
  generateSecureToken,
  hashString,
  
  // Type guards
  isString,
  isNumber,
  isBoolean,
  isObject,
  isArray,
};
