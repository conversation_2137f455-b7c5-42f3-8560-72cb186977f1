require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const { prisma, auth, validation, utils, redis } = require('@shop/shared');
const salesRoutes = require('./routes/sales');
const paymentsRoutes = require('./routes/payments');
const receiptsRoutes = require('./routes/receipts');

const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Authentication middleware for protected routes
const authenticateRequest = (req, res, next) => {
  try {
    const userId = req.headers['x-user-id'];
    const roles = req.headers['x-user-roles'] ? JSON.parse(req.headers['x-user-roles']) : [];
    const permissions = req.headers['x-user-permissions'] ? JSON.parse(req.headers['x-user-permissions']) : [];

    if (userId) {
      req.user = {
        userId,
        roles,
        permissions,
      };
    }
    next();
  } catch (error) {
    next();
  }
};

app.use(authenticateRequest);

// Health check
app.get('/health', (req, res) => {
  res.json({
    service: 'sales-service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});

// Routes
app.use('/sales', salesRoutes);
app.use('/payments', paymentsRoutes);
app.use('/receipts', receiptsRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Sales service error:', err);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';
  
  utils.sendError(res, message, statusCode);
});

// 404 handler
app.use('*', (req, res) => {
  utils.sendError(res, 'Route not found', 404);
});

// Initialize services
const initializeServices = async () => {
  try {
    await redis.connectRedis();
    console.log('✅ Redis connected');
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    console.log('⚠️  Continuing without Redis cache');
  }
};

// Start server
const startServer = async () => {
  await initializeServices();
  
  app.listen(PORT, () => {
    console.log(`💰 Sales Service running on port ${PORT}`);
  });
};

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  await redis.disconnectRedis();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  await redis.disconnectRedis();
  process.exit(0);
});

startServer().catch((error) => {
  console.error('❌ Failed to start Sales Service:', error);
  process.exit(1);
});
