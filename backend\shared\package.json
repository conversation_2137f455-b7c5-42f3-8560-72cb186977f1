{"name": "@shop/shared", "version": "1.0.0", "description": "Shared utilities and Prisma schema for shop software system", "main": "index.js", "scripts": {"dev": "nodemon src/index.js", "build": "tsc", "start": "node dist/index.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "test": "jest"}, "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "redis": "^4.6.11", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^5.7.1", "typescript": "^5.3.3"}, "prisma": {"seed": "node prisma/seed.js"}}