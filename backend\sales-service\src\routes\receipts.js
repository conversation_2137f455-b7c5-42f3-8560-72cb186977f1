const express = require('express');
const PDFDocument = require('pdfkit');
const QRCode = require('qrcode');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Authentication middleware
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return utils.sendError(res, 'Authentication required', 401);
  }
  next();
};

// Permission middleware
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      return utils.sendError(res, 'Insufficient permissions', 403);
    }
    next();
  };
};

/**
 * @swagger
 * /receipts/{saleId}:
 *   get:
 *     summary: Get receipt for a sale
 *     tags: [Receipts]
 *     parameters:
 *       - in: path
 *         name: saleId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Receipt retrieved successfully
 *       404:
 *         description: Receipt not found
 */
router.get('/:saleId', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { saleId } = req.params;

  const receipt = await prisma.receipt.findUnique({
    where: { saleId },
    include: {
      sale: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            }
          },
          customer: true,
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          },
          payments: true,
        }
      }
    }
  });

  if (!receipt) {
    return utils.sendError(res, 'Receipt not found', 404);
  }

  utils.sendSuccess(res, { receipt });
}));

/**
 * @swagger
 * /receipts/{saleId}/generate:
 *   post:
 *     summary: Generate receipt for a sale
 *     tags: [Receipts]
 *     parameters:
 *       - in: path
 *         name: saleId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       201:
 *         description: Receipt generated successfully
 */
router.post('/:saleId/generate', requireAuth, requirePermission('sales:create'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { saleId } = req.params;

  // Get sale data
  const sale = await prisma.sale.findUnique({
    where: { id: saleId },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      },
      customer: true,
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            }
          }
        }
      },
      payments: true,
    }
  });

  if (!sale) {
    return utils.sendError(res, 'Sale not found', 404);
  }

  // Generate receipt data
  const receiptData = {
    saleNumber: sale.saleNumber,
    date: sale.createdAt,
    cashier: `${sale.user.firstName} ${sale.user.lastName}`,
    customer: sale.customer ? {
      name: `${sale.customer.firstName} ${sale.customer.lastName}`,
      email: sale.customer.email,
      phone: sale.customer.phone,
    } : null,
    items: sale.items.map(item => ({
      name: item.product.name,
      sku: item.product.sku,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      total: item.total,
    })),
    subtotal: sale.subtotal,
    tax: sale.tax,
    discount: sale.discount,
    total: sale.total,
    payments: sale.payments.map(payment => ({
      method: payment.method,
      amount: payment.amount,
      reference: payment.reference,
    })),
    store: {
      name: process.env.STORE_NAME || 'Shop Software Store',
      address: process.env.STORE_ADDRESS || '123 Main St, City, State 12345',
      phone: process.env.STORE_PHONE || '(*************',
      email: process.env.STORE_EMAIL || '<EMAIL>',
    }
  };

  // Create or update receipt
  const receipt = await prisma.receipt.upsert({
    where: { saleId },
    update: {
      receiptData,
    },
    create: {
      saleId,
      receiptData,
    }
  });

  utils.sendSuccess(res, { receipt }, 'Receipt generated successfully', 201);
}));

/**
 * @swagger
 * /receipts/{saleId}/pdf:
 *   get:
 *     summary: Generate PDF receipt for a sale
 *     tags: [Receipts]
 *     parameters:
 *       - in: path
 *         name: saleId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: PDF receipt generated successfully
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/:saleId/pdf', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { saleId } = req.params;

  // Get receipt data
  let receipt = await prisma.receipt.findUnique({
    where: { saleId },
    include: {
      sale: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            }
          },
          customer: true,
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          },
          payments: true,
        }
      }
    }
  });

  if (!receipt) {
    // Generate receipt if it doesn't exist
    const sale = await prisma.sale.findUnique({
      where: { id: saleId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        },
        customer: true,
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        },
        payments: true,
      }
    });

    if (!sale) {
      return utils.sendError(res, 'Sale not found', 404);
    }

    const receiptData = {
      saleNumber: sale.saleNumber,
      date: sale.createdAt,
      cashier: `${sale.user.firstName} ${sale.user.lastName}`,
      customer: sale.customer ? {
        name: `${sale.customer.firstName} ${sale.customer.lastName}`,
        email: sale.customer.email,
        phone: sale.customer.phone,
      } : null,
      items: sale.items.map(item => ({
        name: item.product.name,
        sku: item.product.sku,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.total,
      })),
      subtotal: sale.subtotal,
      tax: sale.tax,
      discount: sale.discount,
      total: sale.total,
      payments: sale.payments.map(payment => ({
        method: payment.method,
        amount: payment.amount,
        reference: payment.reference,
      })),
      store: {
        name: process.env.STORE_NAME || 'Shop Software Store',
        address: process.env.STORE_ADDRESS || '123 Main St, City, State 12345',
        phone: process.env.STORE_PHONE || '(*************',
        email: process.env.STORE_EMAIL || '<EMAIL>',
      }
    };

    receipt = await prisma.receipt.create({
      data: {
        saleId,
        receiptData,
      }
    });
    receipt.sale = sale;
  }

  // Generate PDF
  const doc = new PDFDocument({ size: 'A4', margin: 50 });
  
  // Set response headers
  res.setHeader('Content-Type', 'application/pdf');
  res.setHeader('Content-Disposition', `attachment; filename="receipt-${receipt.receiptData.saleNumber}.pdf"`);
  
  // Pipe PDF to response
  doc.pipe(res);

  // Generate QR code for the receipt
  const qrCodeData = `${process.env.STORE_URL || 'https://shop.com'}/receipts/${saleId}`;
  const qrCodeBuffer = await QRCode.toBuffer(qrCodeData, { width: 100 });

  // Header
  doc.fontSize(20).text(receipt.receiptData.store.name, { align: 'center' });
  doc.fontSize(12).text(receipt.receiptData.store.address, { align: 'center' });
  doc.text(receipt.receiptData.store.phone, { align: 'center' });
  doc.text(receipt.receiptData.store.email, { align: 'center' });
  
  doc.moveDown();
  doc.text('RECEIPT', { align: 'center', underline: true });
  doc.moveDown();

  // Sale details
  doc.text(`Sale #: ${receipt.receiptData.saleNumber}`);
  doc.text(`Date: ${new Date(receipt.receiptData.date).toLocaleString()}`);
  doc.text(`Cashier: ${receipt.receiptData.cashier}`);
  
  if (receipt.receiptData.customer) {
    doc.text(`Customer: ${receipt.receiptData.customer.name}`);
    if (receipt.receiptData.customer.email) {
      doc.text(`Email: ${receipt.receiptData.customer.email}`);
    }
    if (receipt.receiptData.customer.phone) {
      doc.text(`Phone: ${receipt.receiptData.customer.phone}`);
    }
  }

  doc.moveDown();
  doc.text('ITEMS', { underline: true });
  doc.moveDown();

  // Items table header
  const tableTop = doc.y;
  doc.text('Item', 50, tableTop);
  doc.text('Qty', 250, tableTop);
  doc.text('Price', 300, tableTop);
  doc.text('Total', 400, tableTop);
  
  doc.moveTo(50, doc.y + 5).lineTo(500, doc.y + 5).stroke();
  doc.moveDown();

  // Items
  let itemY = doc.y;
  receipt.receiptData.items.forEach(item => {
    doc.text(item.name, 50, itemY);
    doc.text(item.quantity.toString(), 250, itemY);
    doc.text(utils.formatCurrency(item.unitPrice), 300, itemY);
    doc.text(utils.formatCurrency(item.total), 400, itemY);
    itemY += 20;
  });

  doc.y = itemY + 10;
  doc.moveTo(50, doc.y).lineTo(500, doc.y).stroke();
  doc.moveDown();

  // Totals
  const totalsX = 350;
  doc.text(`Subtotal: ${utils.formatCurrency(receipt.receiptData.subtotal)}`, totalsX);
  if (receipt.receiptData.discount > 0) {
    doc.text(`Discount: -${utils.formatCurrency(receipt.receiptData.discount)}`, totalsX);
  }
  if (receipt.receiptData.tax > 0) {
    doc.text(`Tax: ${utils.formatCurrency(receipt.receiptData.tax)}`, totalsX);
  }
  doc.fontSize(14).text(`TOTAL: ${utils.formatCurrency(receipt.receiptData.total)}`, totalsX, doc.y + 5);
  doc.fontSize(12);

  doc.moveDown();

  // Payments
  if (receipt.receiptData.payments.length > 0) {
    doc.text('PAYMENTS', { underline: true });
    receipt.receiptData.payments.forEach(payment => {
      doc.text(`${payment.method.toUpperCase()}: ${utils.formatCurrency(payment.amount)}`);
      if (payment.reference) {
        doc.text(`  Ref: ${payment.reference}`);
      }
    });
    doc.moveDown();
  }

  // QR Code
  doc.image(qrCodeBuffer, 450, doc.y, { width: 80 });
  
  // Footer
  doc.fontSize(10);
  doc.text('Thank you for your business!', { align: 'center' });
  doc.text('Please keep this receipt for your records.', { align: 'center' });

  // Mark receipt as printed
  await prisma.receipt.update({
    where: { id: receipt.id },
    data: { printedAt: new Date() }
  });

  doc.end();
}));

/**
 * @swagger
 * /receipts/{saleId}/email:
 *   post:
 *     summary: Email receipt to customer
 *     tags: [Receipts]
 *     parameters:
 *       - in: path
 *         name: saleId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               message:
 *                 type: string
 *     responses:
 *       200:
 *         description: Receipt emailed successfully
 */
router.post('/:saleId/email', requireAuth, requirePermission('sales:create'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { saleId } = req.params;
  const { email, message } = req.body;

  if (!utils.isValidEmail(email)) {
    return utils.sendError(res, 'Valid email address required', 400);
  }

  // Get receipt
  const receipt = await prisma.receipt.findUnique({
    where: { saleId },
    include: {
      sale: {
        include: {
          customer: true
        }
      }
    }
  });

  if (!receipt) {
    return utils.sendError(res, 'Receipt not found', 404);
  }

  // In a real implementation, you would integrate with an email service
  // For now, we'll just log the email action
  console.log(`Email receipt: Sale ${receipt.receiptData.saleNumber} to ${email}`);
  console.log(`Message: ${message || 'Thank you for your purchase!'}`);

  // Here you would typically:
  // 1. Generate PDF receipt
  // 2. Send email with PDF attachment
  // 3. Log the email action

  utils.sendSuccess(res, null, 'Receipt emailed successfully');
}));

module.exports = router;
