const express = require('express');
const axios = require('axios');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Authentication middleware
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return utils.sendError(res, 'Authentication required', 401);
  }
  next();
};

// Permission middleware
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      return utils.sendError(res, 'Insufficient permissions', 403);
    }
    next();
  };
};

// Service URLs
const SALES_SERVICE_URL = process.env.SALES_SERVICE_URL || 'http://localhost:3003';

/**
 * @swagger
 * /customers:
 *   get:
 *     summary: Get all customers
 *     tags: [Customers]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customers retrieved successfully
 */
router.get('/', requireAuth, requirePermission('customers:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req, res) => {
  const { page, limit, search } = req.query;
  const skip = (page - 1) * limit;

  const where = { isActive: true };

  if (search) {
    where.OR = [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [customers, total] = await Promise.all([
    prisma.customer.findMany({
      where,
      skip,
      take: limit,
      include: {
        contacts: true,
        _count: {
          select: {
            sales: true,
            orders: true,
            notes: true,
          }
        }
      },
      orderBy: { lastName: 'asc' }
    }),
    prisma.customer.count({ where })
  ]);

  const customersResponse = customers.map(customer => ({
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
    notesCount: customer._count.notes,
  }));

  utils.sendSuccess(res, {
    customers: customersResponse,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  });
}));

/**
 * @swagger
 * /customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer retrieved successfully
 *       404:
 *         description: Customer not found
 */
router.get('/:id', requireAuth, requirePermission('customers:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  const customer = await prisma.customer.findUnique({
    where: { id },
    include: {
      contacts: true,
      notes: {
        orderBy: { createdAt: 'desc' },
        take: 10
      },
      _count: {
        select: {
          sales: true,
          orders: true,
          notes: true,
        }
      }
    }
  });

  if (!customer) {
    return utils.sendError(res, 'Customer not found', 404);
  }

  const customerResponse = {
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
    notesCount: customer._count.notes,
  };

  utils.sendSuccess(res, { customer: customerResponse });
}));

/**
 * @swagger
 * /customers:
 *   post:
 *     summary: Create new customer
 *     tags: [Customers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               phone:
 *                 type: string
 *               address:
 *                 type: string
 *               city:
 *                 type: string
 *               postalCode:
 *                 type: string
 *     responses:
 *       201:
 *         description: Customer created successfully
 */
router.post('/', requireAuth, requirePermission('customers:create'), validation.validateBody(validation.customerCreateSchema), utils.handleAsyncError(async (req, res) => {
  const customerData = req.body;

  // Check if customer with email already exists
  if (customerData.email) {
    const existingCustomer = await prisma.customer.findUnique({
      where: { email: customerData.email }
    });

    if (existingCustomer) {
      return utils.sendError(res, 'Customer with this email already exists', 400);
    }
  }

  // Create customer
  const customer = await prisma.customer.create({
    data: customerData,
    include: {
      contacts: true,
      _count: {
        select: {
          sales: true,
          orders: true,
          notes: true,
        }
      }
    }
  });

  const customerResponse = {
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
    notesCount: customer._count.notes,
  };

  utils.sendSuccess(res, { customer: customerResponse }, 'Customer created successfully', 201);
}));

/**
 * @swagger
 * /customers/{id}:
 *   put:
 *     summary: Update customer
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               phone:
 *                 type: string
 *               address:
 *                 type: string
 *               city:
 *                 type: string
 *               postalCode:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Customer updated successfully
 */
router.put('/:id', requireAuth, requirePermission('customers:update'), validation.validateParams(validation.idSchema), validation.validateBody(validation.customerUpdateSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if customer exists
  const existingCustomer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!existingCustomer) {
    return utils.sendError(res, 'Customer not found', 404);
  }

  // Check for email conflicts
  if (updateData.email) {
    const conflictCustomer = await prisma.customer.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { email: updateData.email }
        ]
      }
    });

    if (conflictCustomer) {
      return utils.sendError(res, 'Email already exists', 400);
    }
  }

  // Update customer
  const customer = await prisma.customer.update({
    where: { id },
    data: updateData,
    include: {
      contacts: true,
      _count: {
        select: {
          sales: true,
          orders: true,
          notes: true,
        }
      }
    }
  });

  const customerResponse = {
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
    notesCount: customer._count.notes,
  };

  utils.sendSuccess(res, { customer: customerResponse }, 'Customer updated successfully');
}));

/**
 * @swagger
 * /customers/{id}:
 *   delete:
 *     summary: Delete customer
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer deleted successfully
 */
router.delete('/:id', requireAuth, requirePermission('customers:delete'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  // Check if customer exists
  const customer = await prisma.customer.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          sales: true,
          orders: true,
        }
      }
    }
  });

  if (!customer) {
    return utils.sendError(res, 'Customer not found', 404);
  }

  // Check if customer has sales or orders
  if (customer._count.sales > 0 || customer._count.orders > 0) {
    // Soft delete by deactivating
    await prisma.customer.update({
      where: { id },
      data: { isActive: false },
    });
  } else {
    // Hard delete if no sales or orders
    await prisma.customer.delete({
      where: { id }
    });
  }

  utils.sendSuccess(res, null, 'Customer deleted successfully');
}));

/**
 * @swagger
 * /customers/{id}/history:
 *   get:
 *     summary: Get customer purchase history
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Customer history retrieved successfully
 */
router.get('/:id/history', requireAuth, requirePermission('customers:read'), validation.validateParams(validation.idSchema), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const { page, limit } = req.query;

  // Check if customer exists
  const customer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!customer) {
    return utils.sendError(res, 'Customer not found', 404);
  }

  try {
    // Get sales history from sales service
    const salesResponse = await axios.get(`${SALES_SERVICE_URL}/sales`, {
      params: {
        customerId: id,
        page,
        limit,
      },
      headers: {
        'x-user-id': req.user.userId,
        'x-user-roles': JSON.stringify(req.user.roles),
        'x-user-permissions': JSON.stringify(req.user.permissions),
      }
    });

    const salesHistory = salesResponse.data.data;

    // Get orders from local database
    const skip = (page - 1) * limit;
    const [orders, ordersTotal] = await Promise.all([
      prisma.order.findMany({
        where: { customerId: id },
        skip,
        take: limit,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.order.count({ where: { customerId: id } })
    ]);

    // Calculate customer statistics
    const totalSpent = salesHistory.sales?.reduce((sum, sale) => sum + sale.total, 0) || 0;
    const totalOrders = ordersTotal;
    const totalSales = salesHistory.pagination?.total || 0;

    utils.sendSuccess(res, {
      customer,
      salesHistory,
      orders,
      ordersPagination: {
        page,
        limit,
        total: ordersTotal,
        totalPages: Math.ceil(ordersTotal / limit),
      },
      statistics: {
        totalSpent,
        totalSales,
        totalOrders,
        averageOrderValue: totalSales > 0 ? totalSpent / totalSales : 0,
      }
    });
  } catch (error) {
    console.error('Failed to fetch sales history:', error.message);
    
    // Return orders only if sales service is unavailable
    const skip = (page - 1) * limit;
    const [orders, ordersTotal] = await Promise.all([
      prisma.order.findMany({
        where: { customerId: id },
        skip,
        take: limit,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.order.count({ where: { customerId: id } })
    ]);

    utils.sendSuccess(res, {
      customer,
      orders,
      ordersPagination: {
        page,
        limit,
        total: ordersTotal,
        totalPages: Math.ceil(ordersTotal / limit),
      },
      note: 'Sales history unavailable - showing orders only'
    });
  }
}));

module.exports = router;
