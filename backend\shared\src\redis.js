const redis = require('redis');

let client;

const connectRedis = async () => {
  if (!client) {
    client = redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
    });

    client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    client.on('connect', () => {
      console.log('Connected to Redis');
    });

    await client.connect();
  }
  return client;
};

const getRedisClient = () => {
  if (!client) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return client;
};

const disconnectRedis = async () => {
  if (client) {
    await client.disconnect();
    client = null;
  }
};

module.exports = {
  connectRedis,
  getRedisClient,
  disconnectRedis,
};
