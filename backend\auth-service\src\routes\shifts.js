const express = require('express');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Apply authentication to all shift routes
router.use(auth.authenticateToken);

/**
 * @swagger
 * /shifts:
 *   get:
 *     summary: Get shifts
 *     tags: [Shifts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed]
 *     responses:
 *       200:
 *         description: Shifts retrieved successfully
 */
router.get('/', validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req, res) => {
  const { page, limit, userId, status } = req.query;
  const skip = (page - 1) * limit;

  const where = {};
  if (userId) where.userId = userId;
  if (status) where.status = status;

  // Non-admin users can only see their own shifts
  if (!req.user.roles.includes('admin')) {
    where.userId = req.user.userId;
  }

  const [shifts, total] = await Promise.all([
    prisma.shift.findMany({
      where,
      skip,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        }
      },
      orderBy: { startTime: 'desc' }
    }),
    prisma.shift.count({ where })
  ]);

  utils.sendSuccess(res, {
    shifts,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  });
}));

/**
 * @swagger
 * /shifts/{id}:
 *   get:
 *     summary: Get shift by ID
 *     tags: [Shifts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Shift retrieved successfully
 *       404:
 *         description: Shift not found
 */
router.get('/:id', validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  const shift = await prisma.shift.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      }
    }
  });

  if (!shift) {
    return utils.sendError(res, 'Shift not found', 404);
  }

  // Non-admin users can only see their own shifts
  if (!req.user.roles.includes('admin') && shift.userId !== req.user.userId) {
    return utils.sendError(res, 'Access denied', 403);
  }

  utils.sendSuccess(res, { shift });
}));

/**
 * @swagger
 * /shifts/start:
 *   post:
 *     summary: Start a new shift
 *     tags: [Shifts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Shift started successfully
 *       400:
 *         description: User already has an active shift
 */
router.post('/start', utils.handleAsyncError(async (req, res) => {
  const { notes } = req.body;
  const userId = req.user.userId;

  // Check if user already has an active shift
  const activeShift = await prisma.shift.findFirst({
    where: {
      userId,
      status: 'active'
    }
  });

  if (activeShift) {
    return utils.sendError(res, 'User already has an active shift', 400);
  }

  // Create new shift
  const shift = await prisma.shift.create({
    data: {
      userId,
      startTime: new Date(),
      notes,
      status: 'active',
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      }
    }
  });

  utils.sendSuccess(res, { shift }, 'Shift started successfully', 201);
}));

/**
 * @swagger
 * /shifts/{id}/end:
 *   put:
 *     summary: End a shift
 *     tags: [Shifts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Shift ended successfully
 *       404:
 *         description: Shift not found
 *       400:
 *         description: Shift already completed
 */
router.put('/:id/end', validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const { notes } = req.body;

  const shift = await prisma.shift.findUnique({
    where: { id }
  });

  if (!shift) {
    return utils.sendError(res, 'Shift not found', 404);
  }

  // Non-admin users can only end their own shifts
  if (!req.user.roles.includes('admin') && shift.userId !== req.user.userId) {
    return utils.sendError(res, 'Access denied', 403);
  }

  if (shift.status === 'completed') {
    return utils.sendError(res, 'Shift already completed', 400);
  }

  // Update shift
  const updatedShift = await prisma.shift.update({
    where: { id },
    data: {
      endTime: new Date(),
      status: 'completed',
      notes: notes || shift.notes,
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      }
    }
  });

  utils.sendSuccess(res, { shift: updatedShift }, 'Shift ended successfully');
}));

/**
 * @swagger
 * /shifts/current:
 *   get:
 *     summary: Get current active shift for user
 *     tags: [Shifts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current shift retrieved successfully
 *       404:
 *         description: No active shift found
 */
router.get('/current', utils.handleAsyncError(async (req, res) => {
  const userId = req.user.userId;

  const shift = await prisma.shift.findFirst({
    where: {
      userId,
      status: 'active'
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      }
    }
  });

  if (!shift) {
    return utils.sendError(res, 'No active shift found', 404);
  }

  utils.sendSuccess(res, { shift });
}));

/**
 * @swagger
 * /shifts/stats:
 *   get:
 *     summary: Get shift statistics
 *     tags: [Shifts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Shift statistics retrieved successfully
 */
router.get('/stats', auth.authorizePermissions('users:read'), utils.handleAsyncError(async (req, res) => {
  const { userId, dateFrom, dateTo } = req.query;

  const where = {
    status: 'completed',
  };

  if (userId) where.userId = userId;
  if (dateFrom || dateTo) {
    where.startTime = {};
    if (dateFrom) where.startTime.gte = new Date(dateFrom);
    if (dateTo) where.startTime.lte = new Date(dateTo);
  }

  const shifts = await prisma.shift.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      }
    }
  });

  // Calculate statistics
  const stats = {
    totalShifts: shifts.length,
    totalHours: 0,
    averageHours: 0,
    byUser: {},
  };

  shifts.forEach(shift => {
    if (shift.endTime) {
      const hours = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
      stats.totalHours += hours;

      const userKey = shift.user.username;
      if (!stats.byUser[userKey]) {
        stats.byUser[userKey] = {
          user: shift.user,
          shifts: 0,
          hours: 0,
        };
      }
      stats.byUser[userKey].shifts += 1;
      stats.byUser[userKey].hours += hours;
    }
  });

  stats.averageHours = stats.totalShifts > 0 ? stats.totalHours / stats.totalShifts : 0;

  utils.sendSuccess(res, { stats });
}));

module.exports = router;
