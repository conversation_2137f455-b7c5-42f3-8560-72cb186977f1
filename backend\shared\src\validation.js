const { z } = require('zod');

// Common validation schemas
const idSchema = z.string().cuid();
const emailSchema = z.string().email();
const phoneSchema = z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number');
const priceSchema = z.number().positive().multipleOf(0.01);
const quantitySchema = z.number().int().positive();

// Product validation schemas
const productCreateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  sku: z.string().min(1).max(100),
  barcode: z.string().optional(),
  price: priceSchema,
  cost: priceSchema.optional(),
  categoryId: idSchema,
});

const productUpdateSchema = productCreateSchema.partial();

// Category validation schemas
const categoryCreateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
});

const categoryUpdateSchema = categoryCreateSchema.partial();

// Stock validation schemas
const stockUpdateSchema = z.object({
  quantity: quantitySchema,
  minThreshold: z.number().int().min(0).optional(),
  maxThreshold: z.number().int().min(0).optional(),
});

// Customer validation schemas
const customerCreateSchema = z.object({
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
});

const customerUpdateSchema = customerCreateSchema.partial();

// Sale validation schemas
const saleItemSchema = z.object({
  productId: idSchema,
  quantity: quantitySchema,
  unitPrice: priceSchema,
});

const saleCreateSchema = z.object({
  customerId: idSchema.optional(),
  items: z.array(saleItemSchema).min(1),
  discount: z.number().min(0).default(0),
  tax: z.number().min(0).default(0),
});

// Order validation schemas
const orderItemSchema = z.object({
  productId: idSchema,
  quantity: quantitySchema,
  unitPrice: priceSchema,
});

const orderCreateSchema = z.object({
  customerId: idSchema,
  items: z.array(orderItemSchema).min(1),
  notes: z.string().optional(),
  expectedDate: z.string().datetime().optional(),
});

const orderUpdateSchema = z.object({
  status: z.enum(['pending', 'processing', 'ready', 'completed', 'cancelled']).optional(),
  notes: z.string().optional(),
  expectedDate: z.string().datetime().optional(),
});

// Payment validation schemas
const paymentCreateSchema = z.object({
  method: z.enum(['cash', 'card', 'digital']),
  amount: priceSchema,
  reference: z.string().optional(),
});

// Notification validation schemas
const notificationCreateSchema = z.object({
  type: z.string().min(1),
  title: z.string().min(1).max(255),
  message: z.string().min(1),
  recipientId: idSchema.optional(),
  recipientType: z.enum(['user', 'role', 'all']).default('user'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  data: z.record(z.any()).optional(),
  scheduledFor: z.string().datetime().optional(),
});

// Pagination schema
const paginationSchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 100)),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Search schema
const searchSchema = z.object({
  q: z.string().optional(),
  category: idSchema.optional(),
  status: z.string().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
});

// Validation middleware
const validateBody = (schema) => {
  return (req, res, next) => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.errors,
      });
    }
  };
};

const validateQuery = (schema) => {
  return (req, res, next) => {
    try {
      req.query = schema.parse(req.query);
      next();
    } catch (error) {
      return res.status(400).json({
        error: 'Query validation failed',
        details: error.errors,
      });
    }
  };
};

const validateParams = (schema) => {
  return (req, res, next) => {
    try {
      req.params = schema.parse(req.params);
      next();
    } catch (error) {
      return res.status(400).json({
        error: 'Parameter validation failed',
        details: error.errors,
      });
    }
  };
};

module.exports = {
  // Schemas
  idSchema,
  emailSchema,
  phoneSchema,
  priceSchema,
  quantitySchema,
  productCreateSchema,
  productUpdateSchema,
  categoryCreateSchema,
  categoryUpdateSchema,
  stockUpdateSchema,
  customerCreateSchema,
  customerUpdateSchema,
  saleCreateSchema,
  orderCreateSchema,
  orderUpdateSchema,
  paymentCreateSchema,
  notificationCreateSchema,
  paginationSchema,
  searchSchema,
  
  // Middleware
  validateBody,
  validateQuery,
  validateParams,
};
