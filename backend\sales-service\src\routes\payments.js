const express = require('express');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Authentication middleware
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return utils.sendError(res, 'Authentication required', 401);
  }
  next();
};

// Permission middleware
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      return utils.sendError(res, 'Insufficient permissions', 403);
    }
    next();
  };
};

/**
 * @swagger
 * /payments:
 *   get:
 *     summary: Get all payments
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: method
 *         schema:
 *           type: string
 *           enum: [cash, card, digital]
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, completed, failed]
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Payments retrieved successfully
 */
router.get('/', requireAuth, requirePermission('sales:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req, res) => {
  const { page, limit, method, status, dateFrom, dateTo } = req.query;
  const skip = (page - 1) * limit;

  const where = {};

  if (method) where.method = method;
  if (status) where.status = status;

  if (dateFrom || dateTo) {
    where.processedAt = {};
    if (dateFrom) where.processedAt.gte = new Date(dateFrom);
    if (dateTo) where.processedAt.lte = new Date(dateTo);
  }

  const [payments, total] = await Promise.all([
    prisma.payment.findMany({
      where,
      skip,
      take: limit,
      include: {
        sale: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                username: true,
              }
            },
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              }
            }
          }
        }
      },
      orderBy: { processedAt: 'desc' }
    }),
    prisma.payment.count({ where })
  ]);

  utils.sendSuccess(res, {
    payments,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  });
}));

/**
 * @swagger
 * /payments/{id}:
 *   get:
 *     summary: Get payment by ID
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Payment retrieved successfully
 *       404:
 *         description: Payment not found
 */
router.get('/:id', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  const payment = await prisma.payment.findUnique({
    where: { id },
    include: {
      sale: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            }
          },
          customer: true,
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          }
        }
      }
    }
  });

  if (!payment) {
    return utils.sendError(res, 'Payment not found', 404);
  }

  utils.sendSuccess(res, { payment });
}));

/**
 * @swagger
 * /payments:
 *   post:
 *     summary: Process a payment for a sale
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - saleId
 *               - method
 *               - amount
 *             properties:
 *               saleId:
 *                 type: string
 *               method:
 *                 type: string
 *                 enum: [cash, card, digital]
 *               amount:
 *                 type: number
 *               reference:
 *                 type: string
 *     responses:
 *       201:
 *         description: Payment processed successfully
 */
router.post('/', requireAuth, requirePermission('sales:create'), validation.validateBody(validation.paymentCreateSchema), utils.handleAsyncError(async (req, res) => {
  const { saleId, method, amount, reference } = req.body;

  // Check if sale exists
  const sale = await prisma.sale.findUnique({
    where: { id: saleId },
    include: {
      payments: true
    }
  });

  if (!sale) {
    return utils.sendError(res, 'Sale not found', 404);
  }

  // Calculate remaining amount
  const totalPaid = sale.payments.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingAmount = sale.total - totalPaid;

  if (amount > remainingAmount) {
    return utils.sendError(res, `Payment amount exceeds remaining balance. Remaining: ${remainingAmount}`, 400);
  }

  // Create payment
  const payment = await prisma.payment.create({
    data: {
      saleId,
      method,
      amount,
      reference,
      status: 'completed', // In a real system, this might be 'pending' initially
    },
    include: {
      sale: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            }
          },
          customer: true,
        }
      }
    }
  });

  utils.sendSuccess(res, { payment }, 'Payment processed successfully', 201);
}));

/**
 * @swagger
 * /payments/{id}/refund:
 *   put:
 *     summary: Process a refund for a payment
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - reason
 *             properties:
 *               amount:
 *                 type: number
 *               reason:
 *                 type: string
 *               reference:
 *                 type: string
 *     responses:
 *       200:
 *         description: Refund processed successfully
 */
router.put('/:id/refund', requireAuth, requirePermission('sales:update'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const { amount, reason, reference } = req.body;

  const payment = await prisma.payment.findUnique({
    where: { id },
    include: {
      sale: true
    }
  });

  if (!payment) {
    return utils.sendError(res, 'Payment not found', 404);
  }

  if (payment.status !== 'completed') {
    return utils.sendError(res, 'Can only refund completed payments', 400);
  }

  if (amount > payment.amount) {
    return utils.sendError(res, 'Refund amount cannot exceed payment amount', 400);
  }

  // Create refund payment (negative amount)
  const refund = await prisma.payment.create({
    data: {
      saleId: payment.saleId,
      method: payment.method,
      amount: -amount,
      reference: reference || `Refund for ${payment.id}`,
      status: 'completed',
    },
    include: {
      sale: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            }
          },
          customer: true,
        }
      }
    }
  });

  // Log the refund
  console.log(`Refund processed: Payment ${id}, Amount: ${amount}, Reason: ${reason}, User: ${req.user.userId}`);

  utils.sendSuccess(res, { refund }, 'Refund processed successfully');
}));

/**
 * @swagger
 * /payments/stats:
 *   get:
 *     summary: Get payment statistics
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *           default: today
 *     responses:
 *       200:
 *         description: Payment statistics retrieved successfully
 */
router.get('/stats', requireAuth, requirePermission('sales:read'), utils.handleAsyncError(async (req, res) => {
  const { period = 'today' } = req.query;

  // Calculate date range based on period
  const now = new Date();
  let startDate;

  switch (period) {
    case 'today':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      break;
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  }

  const where = {
    status: 'completed',
    processedAt: {
      gte: startDate,
    }
  };

  const [payments, totalStats] = await Promise.all([
    prisma.payment.findMany({
      where,
    }),
    prisma.payment.aggregate({
      where,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    })
  ]);

  // Group by payment method
  const paymentsByMethod = utils.groupBy(payments, 'method');
  const methodStats = Object.entries(paymentsByMethod).map(([method, methodPayments]) => ({
    method,
    count: methodPayments.length,
    total: methodPayments.reduce((sum, payment) => sum + payment.amount, 0),
    percentage: (methodPayments.length / payments.length) * 100,
  }));

  // Calculate refunds (negative amounts)
  const refunds = payments.filter(payment => payment.amount < 0);
  const totalRefunds = refunds.reduce((sum, refund) => sum + Math.abs(refund.amount), 0);

  const stats = {
    period,
    dateRange: {
      from: startDate.toISOString(),
      to: now.toISOString(),
    },
    totalPayments: totalStats._count.id,
    totalAmount: totalStats._sum.amount || 0,
    totalRefunds,
    netAmount: (totalStats._sum.amount || 0) + totalRefunds, // Refunds are negative
    averagePayment: totalStats._count.id > 0 ? (totalStats._sum.amount || 0) / totalStats._count.id : 0,
    methodStats,
    refundCount: refunds.length,
  };

  utils.sendSuccess(res, { stats });
}));

module.exports = router;
