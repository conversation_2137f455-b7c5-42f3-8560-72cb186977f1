const express = require('express');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Authentication middleware
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return utils.sendError(res, 'Authentication required', 401);
  }
  next();
};

// Permission middleware
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      return utils.sendError(res, 'Insufficient permissions', 403);
    }
    next();
  };
};

/**
 * @swagger
 * /categories:
 *   get:
 *     summary: Get all categories
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 */
router.get('/', requireAuth, requirePermission('inventory:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req, res) => {
  const { page, limit, search } = req.query;
  const skip = (page - 1) * limit;

  const where = { isActive: true };

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [categories, total] = await Promise.all([
    prisma.category.findMany({
      where,
      skip,
      take: limit,
      include: {
        _count: {
          select: {
            products: true
          }
        }
      },
      orderBy: { name: 'asc' }
    }),
    prisma.category.count({ where })
  ]);

  const categoriesResponse = categories.map(category => ({
    ...category,
    productCount: category._count.products,
  }));

  utils.sendSuccess(res, {
    categories: categoriesResponse,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  });
}));

/**
 * @swagger
 * /categories/{id}:
 *   get:
 *     summary: Get category by ID
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Category retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:id', requireAuth, requirePermission('inventory:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  const category = await prisma.category.findUnique({
    where: { id },
    include: {
      products: {
        where: { isActive: true },
        include: {
          stock: true
        }
      },
      _count: {
        select: {
          products: true
        }
      }
    }
  });

  if (!category) {
    return utils.sendError(res, 'Category not found', 404);
  }

  const categoryResponse = {
    ...category,
    productCount: category._count.products,
  };

  utils.sendSuccess(res, { category: categoryResponse });
}));

/**
 * @swagger
 * /categories:
 *   post:
 *     summary: Create new category
 *     tags: [Categories]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Category created successfully
 */
router.post('/', requireAuth, requirePermission('inventory:create'), validation.validateBody(validation.categoryCreateSchema), utils.handleAsyncError(async (req, res) => {
  const { name, description } = req.body;

  // Check if category already exists
  const existingCategory = await prisma.category.findUnique({
    where: { name }
  });

  if (existingCategory) {
    return utils.sendError(res, 'Category with this name already exists', 400);
  }

  // Create category
  const category = await prisma.category.create({
    data: {
      name,
      description,
    }
  });

  utils.sendSuccess(res, { category }, 'Category created successfully', 201);
}));

/**
 * @swagger
 * /categories/{id}:
 *   put:
 *     summary: Update category
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Category updated successfully
 */
router.put('/:id', requireAuth, requirePermission('inventory:update'), validation.validateParams(validation.idSchema), validation.validateBody(validation.categoryUpdateSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if category exists
  const existingCategory = await prisma.category.findUnique({
    where: { id }
  });

  if (!existingCategory) {
    return utils.sendError(res, 'Category not found', 404);
  }

  // Check for name conflicts
  if (updateData.name) {
    const conflictCategory = await prisma.category.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { name: updateData.name }
        ]
      }
    });

    if (conflictCategory) {
      return utils.sendError(res, 'Category name already exists', 400);
    }
  }

  // Update category
  const category = await prisma.category.update({
    where: { id },
    data: updateData,
  });

  utils.sendSuccess(res, { category }, 'Category updated successfully');
}));

/**
 * @swagger
 * /categories/{id}:
 *   delete:
 *     summary: Delete category
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Category deleted successfully
 */
router.delete('/:id', requireAuth, requirePermission('inventory:delete'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  // Check if category exists
  const category = await prisma.category.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          products: true
        }
      }
    }
  });

  if (!category) {
    return utils.sendError(res, 'Category not found', 404);
  }

  // Check if category has products
  if (category._count.products > 0) {
    return utils.sendError(res, 'Cannot delete category with products. Move products to another category first.', 400);
  }

  // Soft delete by deactivating
  await prisma.category.update({
    where: { id },
    data: { isActive: false },
  });

  utils.sendSuccess(res, null, 'Category deleted successfully');
}));

/**
 * @swagger
 * /categories/list:
 *   get:
 *     summary: Get simple list of categories for dropdowns
 *     tags: [Categories]
 *     responses:
 *       200:
 *         description: Category list retrieved successfully
 */
router.get('/list', requireAuth, requirePermission('inventory:read'), utils.handleAsyncError(async (req, res) => {
  const categories = await prisma.category.findMany({
    where: { isActive: true },
    select: {
      id: true,
      name: true,
    },
    orderBy: { name: 'asc' }
  });

  utils.sendSuccess(res, { categories });
}));

module.exports = router;
