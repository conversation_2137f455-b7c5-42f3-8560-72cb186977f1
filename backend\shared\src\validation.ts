import { z } from 'zod';
import { Request, Response, NextFunction } from 'express';

// Common validation schemas
export const idSchema = z.object({
  id: z.string().cuid(),
});

export const emailSchema = z.string().email();
export const phoneSchema = z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number');
export const priceSchema = z.number().positive().multipleOf(0.01);
export const quantitySchema = z.number().int().positive();

// Product validation schemas
export const productCreateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  sku: z.string().min(1).max(100),
  barcode: z.string().optional(),
  price: priceSchema,
  cost: priceSchema.optional(),
  categoryId: z.string().cuid(),
  initialStock: z.number().int().min(0).default(0),
  minThreshold: z.number().int().min(0).default(10),
  maxThreshold: z.number().int().min(0).optional(),
});

export const productUpdateSchema = productCreateSchema.partial().omit({
  initialStock: true,
});

// Category validation schemas
export const categoryCreateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
});

export const categoryUpdateSchema = categoryCreateSchema.partial().extend({
  isActive: z.boolean().optional(),
});

// Stock validation schemas
export const stockUpdateSchema = z.object({
  quantity: quantitySchema,
  minThreshold: z.number().int().min(0).optional(),
  maxThreshold: z.number().int().min(0).optional(),
});

export const stockAdjustmentSchema = z.object({
  adjustment: z.number().int(),
  reason: z.enum(['restock', 'sale', 'damage', 'theft', 'correction', 'return']),
  notes: z.string().optional(),
});

// Customer validation schemas
export const customerCreateSchema = z.object({
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
});

export const customerUpdateSchema = customerCreateSchema.partial().extend({
  isActive: z.boolean().optional(),
});

// Sale validation schemas
export const saleItemSchema = z.object({
  productId: z.string().cuid(),
  quantity: quantitySchema,
  unitPrice: priceSchema,
});

export const saleCreateSchema = z.object({
  customerId: z.string().cuid().optional(),
  items: z.array(saleItemSchema).min(1),
  discount: z.number().min(0).default(0),
  tax: z.number().min(0).default(0),
  payments: z.array(z.object({
    method: z.enum(['cash', 'card', 'digital']),
    amount: priceSchema,
    reference: z.string().optional(),
  })).optional(),
});

// Order validation schemas
export const orderItemSchema = z.object({
  productId: z.string().cuid(),
  quantity: quantitySchema,
  unitPrice: priceSchema,
});

export const orderCreateSchema = z.object({
  customerId: z.string().cuid(),
  items: z.array(orderItemSchema).min(1),
  notes: z.string().optional(),
  expectedDate: z.string().datetime().optional(),
});

export const orderUpdateSchema = z.object({
  status: z.enum(['pending', 'processing', 'ready', 'completed', 'cancelled']).optional(),
  notes: z.string().optional(),
  expectedDate: z.string().datetime().optional(),
});

// Payment validation schemas
export const paymentCreateSchema = z.object({
  saleId: z.string().cuid(),
  method: z.enum(['cash', 'card', 'digital']),
  amount: priceSchema,
  reference: z.string().optional(),
});

// Notification validation schemas
export const notificationCreateSchema = z.object({
  type: z.string().min(1),
  title: z.string().min(1).max(255),
  message: z.string().min(1),
  recipientId: z.string().cuid().optional(),
  recipientType: z.enum(['user', 'role', 'all']).default('user'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  data: z.record(z.any()).optional(),
  scheduledFor: z.string().datetime().optional(),
});

// Pagination schema
export const paginationSchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 100)),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Search schema
export const searchSchema = z.object({
  q: z.string().optional(),
  category: z.string().cuid().optional(),
  status: z.string().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  priceMin: z.number().positive().optional(),
  priceMax: z.number().positive().optional(),
});

// User validation schemas
export const userCreateSchema = z.object({
  email: emailSchema,
  username: z.string().min(3).max(50),
  password: z.string().min(6),
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  roleIds: z.array(z.string().cuid()).optional(),
});

export const userUpdateSchema = userCreateSchema.partial().omit({
  password: true,
}).extend({
  isActive: z.boolean().optional(),
});

// Role validation schemas
export const roleCreateSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().optional(),
  permissionIds: z.array(z.string().cuid()).optional(),
});

export const roleUpdateSchema = roleCreateSchema.partial();

// Shift validation schemas
export const shiftCreateSchema = z.object({
  notes: z.string().optional(),
});

export const shiftUpdateSchema = z.object({
  notes: z.string().optional(),
});

// Validation middleware factory
export const validateBody = <T>(schema: z.ZodSchema<T>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        });
        return;
      }
      next(error);
    }
  };
};

export const validateQuery = <T>(schema: z.ZodSchema<T>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      req.query = schema.parse(req.query);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          error: 'Query validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        });
        return;
      }
      next(error);
    }
  };
};

export const validateParams = <T>(schema: z.ZodSchema<T>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      req.params = schema.parse(req.params);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          error: 'Parameter validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        });
        return;
      }
      next(error);
    }
  };
};

export default {
  // Schemas
  idSchema,
  emailSchema,
  phoneSchema,
  priceSchema,
  quantitySchema,
  productCreateSchema,
  productUpdateSchema,
  categoryCreateSchema,
  categoryUpdateSchema,
  stockUpdateSchema,
  stockAdjustmentSchema,
  customerCreateSchema,
  customerUpdateSchema,
  saleCreateSchema,
  orderCreateSchema,
  orderUpdateSchema,
  paymentCreateSchema,
  notificationCreateSchema,
  paginationSchema,
  searchSchema,
  userCreateSchema,
  userUpdateSchema,
  roleCreateSchema,
  roleUpdateSchema,
  shiftCreateSchema,
  shiftUpdateSchema,
  
  // Middleware
  validateBody,
  validateQuery,
  validateParams,
};
