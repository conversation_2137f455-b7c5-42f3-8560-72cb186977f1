const express = require('express');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Apply authentication to all user routes
router.use(auth.authenticateToken);

/**
 * @swagger
 * /users:
 *   get:
 *     summary: Get all users
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/', auth.authorizePermissions('users:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req, res) => {
  const { page, limit, search } = req.query;
  const skip = (page - 1) * limit;

  const where = search ? {
    OR: [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { username: { contains: search, mode: 'insensitive' } },
    ]
  } : {};

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      skip,
      take: limit,
      include: {
        roles: {
          include: {
            role: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.user.count({ where })
  ]);

  // Remove passwords from response
  const usersResponse = users.map(user => {
    const { password, ...userWithoutPassword } = user;
    return {
      ...userWithoutPassword,
      roles: user.roles.map(ur => ur.role.name)
    };
  });

  utils.sendSuccess(res, {
    users: usersResponse,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  });
}));

/**
 * @swagger
 * /users/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       404:
 *         description: User not found
 */
router.get('/:id', auth.authorizePermissions('users:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id },
    include: {
      roles: {
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      }
    }
  });

  if (!user) {
    return utils.sendError(res, 'User not found', 404);
  }

  const roles = user.roles.map(ur => ur.role.name);
  const permissions = user.roles.flatMap(ur => 
    ur.role.permissions.map(rp => rp.permission.name)
  );

  // Remove password from response
  const { password, ...userResponse } = user;

  utils.sendSuccess(res, {
    ...userResponse,
    roles,
    permissions,
  });
}));

/**
 * @swagger
 * /users:
 *   post:
 *     summary: Create new user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - username
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               roleIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: User created successfully
 */
router.post('/', auth.authorizePermissions('users:create'), validation.validateBody(auth.registerSchema), utils.handleAsyncError(async (req, res) => {
  const { email, username, password, firstName, lastName, roleIds = [] } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [{ email }, { username }]
    }
  });

  if (existingUser) {
    return utils.sendError(res, 'User with this email or username already exists', 400);
  }

  // Hash password
  const hashedPassword = await auth.hashPassword(password);

  // Create user
  const user = await prisma.user.create({
    data: {
      email,
      username,
      password: hashedPassword,
      firstName,
      lastName,
    }
  });

  // Assign roles
  if (roleIds.length > 0) {
    const roleAssignments = roleIds.map(roleId => ({
      userId: user.id,
      roleId,
    }));

    await prisma.userRole.createMany({
      data: roleAssignments,
    });
  } else {
    // Assign default worker role
    const workerRole = await prisma.role.findUnique({
      where: { name: 'worker' }
    });

    if (workerRole) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: workerRole.id,
        }
      });
    }
  }

  // Remove password from response
  const { password: _, ...userResponse } = user;

  utils.sendSuccess(res, { user: userResponse }, 'User created successfully', 201);
}));

/**
 * @swagger
 * /users/{id}:
 *   put:
 *     summary: Update user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: User updated successfully
 */
router.put('/:id', auth.authorizePermissions('users:update'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const updateData = utils.pick(req.body, ['email', 'username', 'firstName', 'lastName', 'isActive']);

  // Check if user exists
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    return utils.sendError(res, 'User not found', 404);
  }

  // Check for email/username conflicts
  if (updateData.email || updateData.username) {
    const conflictUser = await prisma.user.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          {
            OR: [
              updateData.email ? { email: updateData.email } : {},
              updateData.username ? { username: updateData.username } : {},
            ].filter(condition => Object.keys(condition).length > 0)
          }
        ]
      }
    });

    if (conflictUser) {
      return utils.sendError(res, 'Email or username already exists', 400);
    }
  }

  // Update user
  const user = await prisma.user.update({
    where: { id },
    data: updateData,
  });

  // Remove password from response
  const { password, ...userResponse } = user;

  utils.sendSuccess(res, { user: userResponse }, 'User updated successfully');
}));

/**
 * @swagger
 * /users/{id}:
 *   delete:
 *     summary: Delete user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User deleted successfully
 */
router.delete('/:id', auth.authorizePermissions('users:delete'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    return utils.sendError(res, 'User not found', 404);
  }

  // Soft delete by deactivating
  await prisma.user.update({
    where: { id },
    data: { isActive: false },
  });

  utils.sendSuccess(res, null, 'User deleted successfully');
}));

/**
 * @swagger
 * /users/{id}/roles:
 *   put:
 *     summary: Update user roles
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roleIds
 *             properties:
 *               roleIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: User roles updated successfully
 */
router.put('/:id/roles', auth.authorizePermissions('users:update'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const { roleIds } = req.body;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    return utils.sendError(res, 'User not found', 404);
  }

  // Remove existing roles
  await prisma.userRole.deleteMany({
    where: { userId: id }
  });

  // Add new roles
  if (roleIds && roleIds.length > 0) {
    const roleAssignments = roleIds.map(roleId => ({
      userId: id,
      roleId,
    }));

    await prisma.userRole.createMany({
      data: roleAssignments,
    });
  }

  utils.sendSuccess(res, null, 'User roles updated successfully');
}));

module.exports = router;
