import express from 'express';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared';
import { sendEmail, sendPushNotification } from '../services/notificationSender';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

/**
 * @swagger
 * /notifications:
 *   get:
 *     summary: Get notifications for user
 *     tags: [Notifications]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 */
router.get('/', requireAuth, validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { page, limit, unreadOnly } = req.query as any;
  const skip = (page - 1) * limit;
  const userId = req.user!.userId;

  const where: any = {
    OR: [
      { recipientId: userId, recipientType: 'user' },
      { recipientType: 'all' },
      { 
        recipientType: 'role',
        recipientId: { in: req.user!.roles }
      }
    ]
  };

  if (unreadOnly === 'true') {
    where.readAt = null;
  }

  const [notifications, total] = await Promise.all([
    prisma.notification.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' }
    }),
    prisma.notification.count({ where })
  ]);

  utils.sendSuccess(res, {
    notifications,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: skip + limit < total,
      hasPrev: page > 1,
    }
  });
}));

/**
 * @swagger
 * /notifications:
 *   post:
 *     summary: Create new notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *               - message
 *             properties:
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               recipientId:
 *                 type: string
 *               recipientType:
 *                 type: string
 *                 enum: [user, role, all]
 *                 default: user
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, urgent]
 *                 default: normal
 *               data:
 *                 type: object
 *               scheduledFor:
 *                 type: string
 *                 format: date-time
 *               sendEmail:
 *                 type: boolean
 *                 default: false
 *               sendPush:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       201:
 *         description: Notification created successfully
 */
router.post('/', requireAuth, validation.validateBody(validation.notificationCreateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { 
    type, 
    title, 
    message, 
    recipientId, 
    recipientType = 'user', 
    priority = 'normal', 
    data, 
    scheduledFor,
    sendEmail = false,
    sendPush = true
  } = req.body;

  // Create notification
  const notification = await prisma.notification.create({
    data: {
      type,
      title,
      message,
      recipientId,
      recipientType,
      priority,
      data: data || {},
      scheduledFor: scheduledFor ? new Date(scheduledFor) : null,
    }
  });

  // Send immediately if not scheduled
  if (!scheduledFor) {
    try {
      // Get recipients
      let recipients: any[] = [];

      if (recipientType === 'user' && recipientId) {
        const user = await prisma.user.findUnique({
          where: { id: recipientId },
          select: { id: true, email: true, firstName: true, lastName: true }
        });
        if (user) recipients = [user];
      } else if (recipientType === 'role' && recipientId) {
        const users = await prisma.user.findMany({
          where: {
            roles: {
              some: {
                role: {
                  name: recipientId
                }
              }
            }
          },
          select: { id: true, email: true, firstName: true, lastName: true }
        });
        recipients = users;
      } else if (recipientType === 'all') {
        const users = await prisma.user.findMany({
          where: { isActive: true },
          select: { id: true, email: true, firstName: true, lastName: true }
        });
        recipients = users;
      }

      // Send notifications
      for (const recipient of recipients) {
        if (sendEmail && recipient.email) {
          try {
            await sendEmail({
              to: recipient.email,
              subject: title,
              text: message,
              html: `
                <h2>${title}</h2>
                <p>${message}</p>
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
              `
            });
          } catch (error) {
            console.error('Email send failed:', error);
          }
        }

        if (sendPush) {
          try {
            await sendPushNotification(recipient.id, {
              title,
              body: message,
              data: data || {}
            });
          } catch (error) {
            console.error('Push notification failed:', error);
          }
        }
      }

      // Mark as sent
      await prisma.notification.update({
        where: { id: notification.id },
        data: { sentAt: new Date() }
      });
    } catch (error) {
      console.error('Notification sending failed:', error);
    }
  }

  utils.sendSuccess(res, { notification }, 'Notification created successfully', 201);
}));

/**
 * @swagger
 * /notifications/{id}/read:
 *   put:
 *     summary: Mark notification as read
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification marked as read
 */
router.put('/:id/read', requireAuth, validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { id } = req.params;
  const userId = req.user!.userId;

  const notification = await prisma.notification.findUnique({
    where: { id }
  });

  if (!notification) {
    utils.sendError(res, 'Notification not found', 404);
    return;
  }

  // Check if user can read this notification
  const canRead = notification.recipientType === 'all' ||
    (notification.recipientType === 'user' && notification.recipientId === userId) ||
    (notification.recipientType === 'role' && req.user!.roles.includes(notification.recipientId || ''));

  if (!canRead) {
    utils.sendError(res, 'Access denied', 403);
    return;
  }

  // Mark as read
  const updatedNotification = await prisma.notification.update({
    where: { id },
    data: { readAt: new Date() }
  });

  utils.sendSuccess(res, { notification: updatedNotification }, 'Notification marked as read');
}));

/**
 * @swagger
 * /notifications/mark-all-read:
 *   put:
 *     summary: Mark all notifications as read for user
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: All notifications marked as read
 */
router.put('/mark-all-read', requireAuth, utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const userId = req.user!.userId;

  await prisma.notification.updateMany({
    where: {
      OR: [
        { recipientId: userId, recipientType: 'user' },
        { recipientType: 'all' },
        { 
          recipientType: 'role',
          recipientId: { in: req.user!.roles }
        }
      ],
      readAt: null
    },
    data: { readAt: new Date() }
  });

  utils.sendSuccess(res, null, 'All notifications marked as read');
}));

/**
 * @swagger
 * /notifications/unread-count:
 *   get:
 *     summary: Get unread notification count for user
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Unread count retrieved successfully
 */
router.get('/unread-count', requireAuth, utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const userId = req.user!.userId;

  const count = await prisma.notification.count({
    where: {
      OR: [
        { recipientId: userId, recipientType: 'user' },
        { recipientType: 'all' },
        { 
          recipientType: 'role',
          recipientId: { in: req.user!.roles }
        }
      ],
      readAt: null
    }
  });

  utils.sendSuccess(res, { count });
}));

/**
 * @swagger
 * /notifications/{id}:
 *   delete:
 *     summary: Delete notification
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification deleted successfully
 */
router.delete('/:id', requireAuth, validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { id } = req.params;

  // Only allow deletion of own notifications or admin
  const notification = await prisma.notification.findUnique({
    where: { id }
  });

  if (!notification) {
    utils.sendError(res, 'Notification not found', 404);
    return;
  }

  const isAdmin = req.user!.roles.includes('admin');
  const isOwn = notification.recipientType === 'user' && notification.recipientId === req.user!.userId;

  if (!isAdmin && !isOwn) {
    utils.sendError(res, 'Access denied', 403);
    return;
  }

  await prisma.notification.delete({
    where: { id }
  });

  utils.sendSuccess(res, null, 'Notification deleted successfully');
}));

export default router;
