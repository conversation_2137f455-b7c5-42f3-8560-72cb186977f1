{"name": "@shop/api-gateway", "version": "1.0.0", "description": "API Gateway for shop software system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@shop/shared": "file:../shared", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "http-proxy-middleware": "^2.0.6", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/node": "^20.10.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3", "jest": "^29.7.0", "ts-jest": "^29.1.1"}}