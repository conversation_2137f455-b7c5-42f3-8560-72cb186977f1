{"name": "@shop/api-gateway", "version": "1.0.0", "description": "API Gateway for shop software system", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step required'", "test": "jest"}, "dependencies": {"@shop/shared": "file:../shared", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "http-proxy-middleware": "^2.0.6", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}