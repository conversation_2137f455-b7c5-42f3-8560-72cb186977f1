{"name": "shop-software-system", "version": "1.0.0", "description": "Complete microservices-based shop software system", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:inventory\" \"npm run dev:sales\" \"npm run dev:orders\" \"npm run dev:customers\" \"npm run dev:reporting\" \"npm run dev:notifications\"", "dev:gateway": "cd backend/api-gateway && npm run dev", "dev:auth": "cd backend/auth-service && npm run dev", "dev:inventory": "cd backend/inventory-service && npm run dev", "dev:sales": "cd backend/sales-service && npm run dev", "dev:orders": "cd backend/order-service && npm run dev", "dev:customers": "cd backend/customer-service && npm run dev", "dev:reporting": "cd backend/reporting-service && npm run dev", "dev:notifications": "cd backend/notification-service && npm run dev", "build": "npm run build:backend && npm run build:frontend && npm run build:mobile", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:mobile": "cd mobile && npm run build", "install:all": "npm install && npm run install:backend && npm run install:frontend && npm run install:mobile", "install:backend": "cd backend/api-gateway && npm install && cd ../auth-service && npm install && cd ../inventory-service && npm install && cd ../sales-service && npm install && cd ../order-service && npm install && cd ../customer-service && npm install && cd ../reporting-service && npm install && cd ../notification-service && npm install", "install:frontend": "cd frontend && npm install", "install:mobile": "cd mobile && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "db:migrate": "cd backend/shared && npx prisma migrate dev", "db:generate": "cd backend/shared && npx prisma generate", "db:seed": "cd backend/shared && npx prisma db seed", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test"}, "keywords": ["shop", "pos", "microservices", "angular", "nodejs", "prisma", "postgresql"], "author": "Shop Software Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.2"}, "workspaces": ["backend/*", "frontend", "mobile"]}