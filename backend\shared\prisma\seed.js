const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create roles
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: 'Administrator with full access',
    },
  });

  const workerRole = await prisma.role.upsert({
    where: { name: 'worker' },
    update: {},
    create: {
      name: 'worker',
      description: 'Worker with limited access',
    },
  });

  console.log('✅ Roles created');

  // Create permissions
  const permissions = [
    // Inventory permissions
    { name: 'inventory:create', resource: 'inventory', action: 'create', description: 'Create inventory items' },
    { name: 'inventory:read', resource: 'inventory', action: 'read', description: 'View inventory items' },
    { name: 'inventory:update', resource: 'inventory', action: 'update', description: 'Update inventory items' },
    { name: 'inventory:delete', resource: 'inventory', action: 'delete', description: 'Delete inventory items' },
    
    // Sales permissions
    { name: 'sales:create', resource: 'sales', action: 'create', description: 'Process sales' },
    { name: 'sales:read', resource: 'sales', action: 'read', description: 'View sales data' },
    { name: 'sales:update', resource: 'sales', action: 'update', description: 'Update sales' },
    { name: 'sales:delete', resource: 'sales', action: 'delete', description: 'Delete sales' },
    
    // Orders permissions
    { name: 'orders:create', resource: 'orders', action: 'create', description: 'Create orders' },
    { name: 'orders:read', resource: 'orders', action: 'read', description: 'View orders' },
    { name: 'orders:update', resource: 'orders', action: 'update', description: 'Update orders' },
    { name: 'orders:delete', resource: 'orders', action: 'delete', description: 'Delete orders' },
    
    // Customers permissions
    { name: 'customers:create', resource: 'customers', action: 'create', description: 'Create customers' },
    { name: 'customers:read', resource: 'customers', action: 'read', description: 'View customers' },
    { name: 'customers:update', resource: 'customers', action: 'update', description: 'Update customers' },
    { name: 'customers:delete', resource: 'customers', action: 'delete', description: 'Delete customers' },
    
    // Reports permissions
    { name: 'reports:read', resource: 'reports', action: 'read', description: 'View reports' },
    { name: 'reports:export', resource: 'reports', action: 'export', description: 'Export reports' },
    
    // Users permissions
    { name: 'users:create', resource: 'users', action: 'create', description: 'Create users' },
    { name: 'users:read', resource: 'users', action: 'read', description: 'View users' },
    { name: 'users:update', resource: 'users', action: 'update', description: 'Update users' },
    { name: 'users:delete', resource: 'users', action: 'delete', description: 'Delete users' },
  ];

  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {},
      create: permission,
    });
  }

  console.log('✅ Permissions created');

  // Assign permissions to roles
  const adminPermissions = permissions.map(p => p.name);
  const workerPermissions = [
    'inventory:read',
    'sales:create',
    'sales:read',
    'orders:create',
    'orders:read',
    'customers:create',
    'customers:read',
    'customers:update',
  ];

  // Admin gets all permissions
  for (const permissionName of adminPermissions) {
    const permission = await prisma.permission.findUnique({ where: { name: permissionName } });
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Worker gets limited permissions
  for (const permissionName of workerPermissions) {
    const permission = await prisma.permission.findUnique({ where: { name: permissionName } });
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: workerRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: workerRole.id,
        permissionId: permission.id,
      },
    });
  }

  console.log('✅ Role permissions assigned');

  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
    },
  });

  // Assign admin role to admin user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id,
    },
  });

  // Create sample worker user
  const workerPassword = await bcrypt.hash('worker123', 12);
  const workerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'worker1',
      password: workerPassword,
      firstName: 'John',
      lastName: 'Worker',
    },
  });

  // Assign worker role to worker user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: workerUser.id,
        roleId: workerRole.id,
      },
    },
    update: {},
    create: {
      userId: workerUser.id,
      roleId: workerRole.id,
    },
  });

  console.log('✅ Default users created');

  // Create sample categories
  const categories = [
    { name: 'Electronics', description: 'Electronic devices and accessories' },
    { name: 'Clothing', description: 'Apparel and fashion items' },
    { name: 'Books', description: 'Books and educational materials' },
    { name: 'Home & Garden', description: 'Home improvement and garden supplies' },
    { name: 'Sports', description: 'Sports equipment and accessories' },
  ];

  const createdCategories = [];
  for (const category of categories) {
    const created = await prisma.category.upsert({
      where: { name: category.name },
      update: {},
      create: category,
    });
    createdCategories.push(created);
  }

  console.log('✅ Categories created');

  // Create sample products
  const products = [
    {
      name: 'Smartphone',
      description: 'Latest model smartphone with advanced features',
      sku: 'ELE-SMR-001',
      price: 699.99,
      cost: 450.00,
      categoryId: createdCategories[0].id,
      stock: { quantity: 25, minThreshold: 5 }
    },
    {
      name: 'Laptop',
      description: 'High-performance laptop for work and gaming',
      sku: 'ELE-LAP-001',
      price: 1299.99,
      cost: 900.00,
      categoryId: createdCategories[0].id,
      stock: { quantity: 15, minThreshold: 3 }
    },
    {
      name: 'T-Shirt',
      description: 'Comfortable cotton t-shirt',
      sku: 'CLO-TSH-001',
      price: 19.99,
      cost: 8.00,
      categoryId: createdCategories[1].id,
      stock: { quantity: 100, minThreshold: 20 }
    },
    {
      name: 'Jeans',
      description: 'Classic blue jeans',
      sku: 'CLO-JEA-001',
      price: 59.99,
      cost: 25.00,
      categoryId: createdCategories[1].id,
      stock: { quantity: 50, minThreshold: 10 }
    },
    {
      name: 'Programming Book',
      description: 'Learn programming fundamentals',
      sku: 'BOO-PRO-001',
      price: 39.99,
      cost: 20.00,
      categoryId: createdCategories[2].id,
      stock: { quantity: 30, minThreshold: 5 }
    },
  ];

  for (const product of products) {
    const { stock, ...productData } = product;
    const createdProduct = await prisma.product.upsert({
      where: { sku: product.sku },
      update: {},
      create: productData,
    });

    await prisma.stock.upsert({
      where: { productId: createdProduct.id },
      update: {},
      create: {
        productId: createdProduct.id,
        ...stock,
      },
    });
  }

  console.log('✅ Products and stock created');

  // Create sample customer
  const customer = await prisma.customer.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      firstName: 'Jane',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Main St',
      city: 'Anytown',
      postalCode: '12345',
    },
  });

  console.log('✅ Sample customer created');

  console.log('🎉 Database seeded successfully!');
  console.log('\nDefault credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Worker: <EMAIL> / worker123');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
