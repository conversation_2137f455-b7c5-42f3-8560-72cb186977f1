import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';

import { prisma, redis, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared';
import orderRoutes from './routes/orders';

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Authentication middleware for protected routes
const authenticateRequest = (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void => {
  try {
    const userId = req.headers['x-user-id'] as string;
    const roles = req.headers['x-user-roles'] ? JSON.parse(req.headers['x-user-roles'] as string) : [];
    const permissions = req.headers['x-user-permissions'] ? JSON.parse(req.headers['x-user-permissions'] as string) : [];

    if (userId) {
      req.user = {
        userId,
        email: '',
        username: '',
        roles,
        permissions,
      };
    }
    next();
  } catch (error) {
    next();
  }
};

app.use(authenticateRequest);

// Health check
app.get('/health', (req, res) => {
  res.json({
    service: 'order-service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});

// Routes
app.use('/orders', orderRoutes);

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Order service error:', err);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';
  
  utils.sendError(res, message, statusCode);
});

// 404 handler
app.use('*', (req, res) => {
  utils.sendError(res, 'Route not found', 404);
});

// Initialize services
const initializeServices = async (): Promise<void> => {
  try {
    await redis.connectRedis();
    console.log('✅ Redis connected');
  } catch (error) {
    console.error('❌ Redis connection failed:', (error as Error).message);
    console.log('⚠️  Continuing without Redis cache');
  }
};

// Start server
const startServer = async (): Promise<void> => {
  await initializeServices();
  
  app.listen(PORT, () => {
    console.log(`📋 Order Service running on port ${PORT}`);
  });
};

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  await redis.disconnectRedis();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  await redis.disconnectRedis();
  process.exit(0);
});

startServer().catch((error) => {
  console.error('❌ Failed to start Order Service:', error);
  process.exit(1);
});
