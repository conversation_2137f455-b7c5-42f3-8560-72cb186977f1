const express = require('express');
const axios = require('axios');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Authentication middleware
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return utils.sendError(res, 'Authentication required', 401);
  }
  next();
};

// Permission middleware
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      return utils.sendError(res, 'Insufficient permissions', 403);
    }
    next();
  };
};

// Service URLs
const INVENTORY_SERVICE_URL = process.env.INVENTORY_SERVICE_URL || 'http://localhost:3002';

/**
 * @swagger
 * /sales:
 *   get:
 *     summary: Get all sales
 *     tags: [Sales]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Sales retrieved successfully
 */
router.get('/', requireAuth, requirePermission('sales:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req, res) => {
  const { page, limit, userId, customerId, dateFrom, dateTo } = req.query;
  const skip = (page - 1) * limit;

  const where = {};

  if (userId) where.userId = userId;
  if (customerId) where.customerId = customerId;

  if (dateFrom || dateTo) {
    where.createdAt = {};
    if (dateFrom) where.createdAt.gte = new Date(dateFrom);
    if (dateTo) where.createdAt.lte = new Date(dateTo);
  }

  // Non-admin users can only see their own sales
  if (!req.user.roles.includes('admin')) {
    where.userId = req.user.userId;
  }

  const [sales, total] = await Promise.all([
    prisma.sale.findMany({
      where,
      skip,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        },
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        },
        payments: true,
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.sale.count({ where })
  ]);

  utils.sendSuccess(res, {
    sales,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  });
}));

/**
 * @swagger
 * /sales/{id}:
 *   get:
 *     summary: Get sale by ID
 *     tags: [Sales]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Sale retrieved successfully
 *       404:
 *         description: Sale not found
 */
router.get('/:id', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  const sale = await prisma.sale.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      },
      customer: true,
      items: {
        include: {
          product: true
        }
      },
      payments: true,
      receipt: true,
    }
  });

  if (!sale) {
    return utils.sendError(res, 'Sale not found', 404);
  }

  // Non-admin users can only see their own sales
  if (!req.user.roles.includes('admin') && sale.userId !== req.user.userId) {
    return utils.sendError(res, 'Access denied', 403);
  }

  utils.sendSuccess(res, { sale });
}));

/**
 * @swagger
 * /sales:
 *   post:
 *     summary: Process a new sale
 *     tags: [Sales]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - items
 *             properties:
 *               customerId:
 *                 type: string
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - productId
 *                     - quantity
 *                     - unitPrice
 *                   properties:
 *                     productId:
 *                       type: string
 *                     quantity:
 *                       type: integer
 *                     unitPrice:
 *                       type: number
 *               discount:
 *                 type: number
 *                 default: 0
 *               tax:
 *                 type: number
 *                 default: 0
 *               payments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - method
 *                     - amount
 *                   properties:
 *                     method:
 *                       type: string
 *                       enum: [cash, card, digital]
 *                     amount:
 *                       type: number
 *                     reference:
 *                       type: string
 *     responses:
 *       201:
 *         description: Sale processed successfully
 */
router.post('/', requireAuth, requirePermission('sales:create'), validation.validateBody(validation.saleCreateSchema), utils.handleAsyncError(async (req, res) => {
  const { customerId, items, discount = 0, tax = 0, payments = [] } = req.body;
  const userId = req.user.userId;

  // Validate items and check stock availability
  const productIds = items.map(item => item.productId);
  
  try {
    // Check product availability via inventory service
    const stockCheckPromises = productIds.map(async (productId) => {
      const response = await axios.get(`${INVENTORY_SERVICE_URL}/stock/${productId}`, {
        headers: {
          'x-user-id': req.user.userId,
          'x-user-roles': JSON.stringify(req.user.roles),
          'x-user-permissions': JSON.stringify(req.user.permissions),
        }
      });
      return response.data.data.stock;
    });

    const stockLevels = await Promise.all(stockCheckPromises);
    
    // Validate stock availability
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const stock = stockLevels[i];
      
      if (!stock || stock.quantity < item.quantity) {
        return utils.sendError(res, `Insufficient stock for product ${item.productId}. Available: ${stock?.quantity || 0}, Requested: ${item.quantity}`, 400);
      }
    }
  } catch (error) {
    console.error('Stock check failed:', error.message);
    return utils.sendError(res, 'Unable to verify stock levels', 500);
  }

  // Calculate totals
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const total = subtotal - discount + tax;

  // Validate payments
  if (payments.length > 0) {
    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);
    if (Math.abs(totalPayments - total) > 0.01) {
      return utils.sendError(res, 'Payment amount does not match sale total', 400);
    }
  }

  // Generate sale number
  const saleNumber = utils.generateSaleNumber();

  try {
    // Create sale with transaction
    const sale = await prisma.$transaction(async (tx) => {
      // Create sale
      const newSale = await tx.sale.create({
        data: {
          saleNumber,
          userId,
          customerId,
          subtotal,
          tax,
          discount,
          total,
          status: 'completed',
        }
      });

      // Create sale items
      const saleItems = await Promise.all(
        items.map(item =>
          tx.saleItem.create({
            data: {
              saleId: newSale.id,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              total: item.quantity * item.unitPrice,
            }
          })
        )
      );

      // Create payments
      const salePayments = await Promise.all(
        payments.map(payment =>
          tx.payment.create({
            data: {
              saleId: newSale.id,
              method: payment.method,
              amount: payment.amount,
              reference: payment.reference,
              status: 'completed',
            }
          })
        )
      );

      return { ...newSale, items: saleItems, payments: salePayments };
    });

    // Update inventory levels via inventory service
    try {
      const inventoryUpdatePromises = items.map(async (item) => {
        await axios.put(`${INVENTORY_SERVICE_URL}/stock/${item.productId}/adjust`, {
          adjustment: -item.quantity,
          reason: 'sale',
          notes: `Sale ${saleNumber}`,
        }, {
          headers: {
            'x-user-id': req.user.userId,
            'x-user-roles': JSON.stringify(req.user.roles),
            'x-user-permissions': JSON.stringify(req.user.permissions),
          }
        });
      });

      await Promise.all(inventoryUpdatePromises);
    } catch (error) {
      console.error('Inventory update failed:', error.message);
      // Note: In a production system, you might want to implement compensation logic here
    }

    // Fetch complete sale data
    const completeSale = await prisma.sale.findUnique({
      where: { id: sale.id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        },
        customer: true,
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        },
        payments: true,
      }
    });

    utils.sendSuccess(res, { sale: completeSale }, 'Sale processed successfully', 201);
  } catch (error) {
    console.error('Sale creation failed:', error);
    return utils.sendError(res, 'Failed to process sale', 500);
  }
}));

/**
 * @swagger
 * /sales/{id}/cancel:
 *   put:
 *     summary: Cancel a sale
 *     tags: [Sales]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               reason:
 *                 type: string
 *               restoreStock:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Sale cancelled successfully
 */
router.put('/:id/cancel', requireAuth, requirePermission('sales:update'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const { reason, restoreStock = true } = req.body;

  const sale = await prisma.sale.findUnique({
    where: { id },
    include: {
      items: true,
    }
  });

  if (!sale) {
    return utils.sendError(res, 'Sale not found', 404);
  }

  if (sale.status === 'cancelled') {
    return utils.sendError(res, 'Sale already cancelled', 400);
  }

  // Non-admin users can only cancel their own sales
  if (!req.user.roles.includes('admin') && sale.userId !== req.user.userId) {
    return utils.sendError(res, 'Access denied', 403);
  }

  try {
    // Update sale status
    const updatedSale = await prisma.sale.update({
      where: { id },
      data: {
        status: 'cancelled',
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        },
        customer: true,
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        },
        payments: true,
      }
    });

    // Restore stock if requested
    if (restoreStock) {
      try {
        const inventoryRestorePromises = sale.items.map(async (item) => {
          await axios.put(`${INVENTORY_SERVICE_URL}/stock/${item.productId}/adjust`, {
            adjustment: item.quantity,
            reason: 'sale_cancellation',
            notes: `Sale cancellation ${sale.saleNumber}: ${reason}`,
          }, {
            headers: {
              'x-user-id': req.user.userId,
              'x-user-roles': JSON.stringify(req.user.roles),
              'x-user-permissions': JSON.stringify(req.user.permissions),
            }
          });
        });

        await Promise.all(inventoryRestorePromises);
      } catch (error) {
        console.error('Stock restoration failed:', error.message);
      }
    }

    utils.sendSuccess(res, { sale: updatedSale }, 'Sale cancelled successfully');
  } catch (error) {
    console.error('Sale cancellation failed:', error);
    return utils.sendError(res, 'Failed to cancel sale', 500);
  }
}));

/**
 * @swagger
 * /sales/stats:
 *   get:
 *     summary: Get sales statistics
 *     tags: [Sales]
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *           default: today
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Sales statistics retrieved successfully
 */
router.get('/stats', requireAuth, requirePermission('sales:read'), utils.handleAsyncError(async (req, res) => {
  const { period = 'today', userId } = req.query;

  // Calculate date range based on period
  const now = new Date();
  let startDate;

  switch (period) {
    case 'today':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      break;
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  }

  const where = {
    status: 'completed',
    createdAt: {
      gte: startDate,
    }
  };

  if (userId) where.userId = userId;

  // Non-admin users can only see their own stats
  if (!req.user.roles.includes('admin')) {
    where.userId = req.user.userId;
  }

  const [sales, totalStats] = await Promise.all([
    prisma.sale.findMany({
      where,
      include: {
        items: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        }
      }
    }),
    prisma.sale.aggregate({
      where,
      _sum: {
        total: true,
        subtotal: true,
        tax: true,
        discount: true,
      },
      _count: {
        id: true,
      }
    })
  ]);

  // Calculate additional statistics
  const totalItems = sales.reduce((sum, sale) => sum + sale.items.length, 0);
  const averageSaleValue = totalStats._count.id > 0 ? totalStats._sum.total / totalStats._count.id : 0;

  // Group by user
  const salesByUser = utils.groupBy(sales, 'userId');
  const userStats = Object.entries(salesByUser).map(([userId, userSales]) => ({
    user: userSales[0].user,
    salesCount: userSales.length,
    totalValue: userSales.reduce((sum, sale) => sum + sale.total, 0),
    averageValue: userSales.reduce((sum, sale) => sum + sale.total, 0) / userSales.length,
  }));

  const stats = {
    period,
    dateRange: {
      from: startDate.toISOString(),
      to: now.toISOString(),
    },
    totalSales: totalStats._count.id,
    totalRevenue: totalStats._sum.total || 0,
    totalSubtotal: totalStats._sum.subtotal || 0,
    totalTax: totalStats._sum.tax || 0,
    totalDiscount: totalStats._sum.discount || 0,
    totalItems,
    averageSaleValue,
    userStats,
  };

  utils.sendSuccess(res, { stats });
}));

module.exports = router;
