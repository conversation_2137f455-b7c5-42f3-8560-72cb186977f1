import { Request } from 'express';

// User types
export interface User {
  userId: string;
  email: string;
  username: string;
  roles: string[];
  permissions: string[];
}

// Extended Request type with user
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: any;
  timestamp: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

// Database types
export interface DatabaseConfig {
  url: string;
}

export interface RedisConfig {
  url: string;
}

// JWT types
export interface JWTPayload {
  userId: string;
  email: string;
  username: string;
  roles: string[];
  permissions: string[];
  iat?: number;
  exp?: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// Cache types
export interface CacheOptions {
  ttl?: number;
  prefix?: string;
}

// Service types
export interface ServiceConfig {
  port: number;
  name: string;
  version: string;
}

// Error types
export interface CustomError extends Error {
  statusCode?: number;
  code?: string;
}

// Stock types
export interface StockAdjustment {
  productId: string;
  adjustment: number;
  reason: string;
  notes?: string;
  userId: string;
}

export interface StockStatus {
  status: 'normal' | 'low' | 'out';
  isLowStock: boolean;
  isOutOfStock: boolean;
}

// Sale types
export interface SaleItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface PaymentMethod {
  method: 'cash' | 'card' | 'digital';
  amount: number;
  reference?: string;
}

// Order types
export interface OrderItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface OrderStatusUpdate {
  status: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
  notes?: string;
  createdBy: string;
}

// Notification types
export interface NotificationData {
  type: string;
  title: string;
  message: string;
  recipientId?: string;
  recipientType: 'user' | 'role' | 'all';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  data?: Record<string, any>;
  scheduledFor?: Date;
}

// Receipt types
export interface ReceiptData {
  saleNumber: string;
  date: Date;
  cashier: string;
  customer?: {
    name: string;
    email?: string;
    phone?: string;
  };
  items: Array<{
    name: string;
    sku: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  payments: Array<{
    method: string;
    amount: number;
    reference?: string;
  }>;
  store: {
    name: string;
    address: string;
    phone: string;
    email: string;
  };
}

// Search types
export interface SearchFilters {
  search?: string;
  category?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  priceMin?: number;
  priceMax?: number;
}

// Statistics types
export interface SalesStats {
  totalSales: number;
  totalRevenue: number;
  totalItems: number;
  averageSaleValue: number;
  period: string;
  dateRange: {
    from: string;
    to: string;
  };
}

export interface InventoryStats {
  totalProducts: number;
  lowStockCount: number;
  outOfStockCount: number;
  totalQuantity: number;
  totalCostValue: number;
  totalRetailValue: number;
  healthyStockCount: number;
  lowStockPercentage: number;
}

// HTTP Service types
export interface HttpServiceOptions {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// File upload types
export interface FileUpload {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

// Email types
export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    contentType: string;
  }>;
}

// Audit types
export interface AuditLog {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: Date;
}

// Configuration types
export interface AppConfig {
  port: number;
  nodeEnv: 'development' | 'production' | 'test';
  database: DatabaseConfig;
  redis: RedisConfig;
  jwt: {
    secret: string;
    expiresIn: string;
    refreshExpiresIn: string;
  };
  cors: {
    origins: string[];
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

// Export all types
export * from '@prisma/client';
