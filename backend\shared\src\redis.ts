import { createClient, RedisClientType } from 'redis';

let client: RedisClientType | null = null;

export const connectRedis = async (): Promise<RedisClientType> => {
  if (!client) {
    client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
    });

    client.on('error', (err: Error) => {
      console.error('Redis Client Error:', err);
    });

    client.on('connect', () => {
      console.log('Connected to Redis');
    });

    await client.connect();
  }
  return client;
};

export const getRedisClient = (): RedisClientType => {
  if (!client) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return client;
};

export const disconnectRedis = async (): Promise<void> => {
  if (client) {
    await client.disconnect();
    client = null;
  }
};

export default {
  connectRedis,
  getRedisClient,
  disconnectRedis,
};
