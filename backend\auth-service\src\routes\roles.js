const express = require('express');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

// Apply authentication to all role routes
router.use(auth.authenticateToken);
router.use(auth.authorizeRoles('admin')); // Only admins can manage roles

/**
 * @swagger
 * /roles:
 *   get:
 *     summary: Get all roles
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Roles retrieved successfully
 */
router.get('/', utils.handleAsyncError(async (req, res) => {
  const roles = await prisma.role.findMany({
    include: {
      permissions: {
        include: {
          permission: true
        }
      },
      _count: {
        select: {
          users: true
        }
      }
    },
    orderBy: { name: 'asc' }
  });

  const rolesResponse = roles.map(role => ({
    ...role,
    permissions: role.permissions.map(rp => rp.permission),
    userCount: role._count.users,
  }));

  utils.sendSuccess(res, { roles: rolesResponse });
}));

/**
 * @swagger
 * /roles/{id}:
 *   get:
 *     summary: Get role by ID
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Role retrieved successfully
 *       404:
 *         description: Role not found
 */
router.get('/:id', validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  const role = await prisma.role.findUnique({
    where: { id },
    include: {
      permissions: {
        include: {
          permission: true
        }
      },
      users: {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              username: true,
              firstName: true,
              lastName: true,
            }
          }
        }
      }
    }
  });

  if (!role) {
    return utils.sendError(res, 'Role not found', 404);
  }

  const roleResponse = {
    ...role,
    permissions: role.permissions.map(rp => rp.permission),
    users: role.users.map(ur => ur.user),
  };

  utils.sendSuccess(res, { role: roleResponse });
}));

/**
 * @swagger
 * /roles:
 *   post:
 *     summary: Create new role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               permissionIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: Role created successfully
 */
router.post('/', utils.handleAsyncError(async (req, res) => {
  const { name, description, permissionIds = [] } = req.body;

  // Check if role already exists
  const existingRole = await prisma.role.findUnique({
    where: { name }
  });

  if (existingRole) {
    return utils.sendError(res, 'Role with this name already exists', 400);
  }

  // Create role
  const role = await prisma.role.create({
    data: {
      name,
      description,
    }
  });

  // Assign permissions
  if (permissionIds.length > 0) {
    const permissionAssignments = permissionIds.map(permissionId => ({
      roleId: role.id,
      permissionId,
    }));

    await prisma.rolePermission.createMany({
      data: permissionAssignments,
    });
  }

  utils.sendSuccess(res, { role }, 'Role created successfully', 201);
}));

/**
 * @swagger
 * /roles/{id}:
 *   put:
 *     summary: Update role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: Role updated successfully
 */
router.put('/:id', validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const updateData = utils.pick(req.body, ['name', 'description']);

  // Check if role exists
  const existingRole = await prisma.role.findUnique({
    where: { id }
  });

  if (!existingRole) {
    return utils.sendError(res, 'Role not found', 404);
  }

  // Check for name conflicts
  if (updateData.name) {
    const conflictRole = await prisma.role.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { name: updateData.name }
        ]
      }
    });

    if (conflictRole) {
      return utils.sendError(res, 'Role name already exists', 400);
    }
  }

  // Update role
  const role = await prisma.role.update({
    where: { id },
    data: updateData,
  });

  utils.sendSuccess(res, { role }, 'Role updated successfully');
}));

/**
 * @swagger
 * /roles/{id}:
 *   delete:
 *     summary: Delete role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Role deleted successfully
 */
router.delete('/:id', validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;

  // Check if role exists
  const role = await prisma.role.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          users: true
        }
      }
    }
  });

  if (!role) {
    return utils.sendError(res, 'Role not found', 404);
  }

  // Prevent deletion of roles with users
  if (role._count.users > 0) {
    return utils.sendError(res, 'Cannot delete role with assigned users', 400);
  }

  // Delete role (this will cascade delete permissions)
  await prisma.role.delete({
    where: { id }
  });

  utils.sendSuccess(res, null, 'Role deleted successfully');
}));

/**
 * @swagger
 * /roles/{id}/permissions:
 *   put:
 *     summary: Update role permissions
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - permissionIds
 *             properties:
 *               permissionIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Role permissions updated successfully
 */
router.put('/:id/permissions', validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req, res) => {
  const { id } = req.params;
  const { permissionIds } = req.body;

  // Check if role exists
  const role = await prisma.role.findUnique({
    where: { id }
  });

  if (!role) {
    return utils.sendError(res, 'Role not found', 404);
  }

  // Remove existing permissions
  await prisma.rolePermission.deleteMany({
    where: { roleId: id }
  });

  // Add new permissions
  if (permissionIds && permissionIds.length > 0) {
    const permissionAssignments = permissionIds.map(permissionId => ({
      roleId: id,
      permissionId,
    }));

    await prisma.rolePermission.createMany({
      data: permissionAssignments,
    });
  }

  utils.sendSuccess(res, null, 'Role permissions updated successfully');
}));

/**
 * @swagger
 * /permissions:
 *   get:
 *     summary: Get all permissions
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Permissions retrieved successfully
 */
router.get('/permissions', utils.handleAsyncError(async (req, res) => {
  const permissions = await prisma.permission.findMany({
    orderBy: [
      { resource: 'asc' },
      { action: 'asc' }
    ]
  });

  // Group permissions by resource
  const groupedPermissions = utils.groupBy(permissions, 'resource');

  utils.sendSuccess(res, { 
    permissions,
    groupedPermissions 
  });
}));

module.exports = router;
