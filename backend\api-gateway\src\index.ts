import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { createProxyMiddleware } from 'http-proxy-middleware';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const app = express();
const PORT = process.env.PORT || 3000;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:4200', 'http://localhost:8100'],
  credentials: true,
}));
app.use(morgan('combined'));
app.use(limiter);
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Service registry
const services = {
  auth: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
  inventory: process.env.INVENTORY_SERVICE_URL || 'http://localhost:3002',
  sales: process.env.SALES_SERVICE_URL || 'http://localhost:3003',
  orders: process.env.ORDER_SERVICE_URL || 'http://localhost:3004',
  customers: process.env.CUSTOMER_SERVICE_URL || 'http://localhost:3005',
  reports: process.env.REPORTING_SERVICE_URL || 'http://localhost:3006',
  notifications: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3007',
};

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Shop Management System API',
      version: '1.0.0',
      description: 'Complete API documentation for the Shop Management System',
    },
    servers: [
      {
        url: `http://localhost:${PORT}`,
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['../*/src/routes/*.ts', '../*/src/routes/*.js'], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(swaggerOptions);

// Swagger UI
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Shop Management API Documentation',
}));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: Object.keys(services),
  });
});

// Service health checks
app.get('/health/services', async (req, res) => {
  const healthChecks = await Promise.allSettled(
    Object.entries(services).map(async ([name, url]) => {
      try {
        const response = await fetch(`${url}/health`, { 
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        return {
          service: name,
          status: response.ok ? 'healthy' : 'unhealthy',
          url,
          responseTime: Date.now(),
        };
      } catch (error) {
        return {
          service: name,
          status: 'unreachable',
          url,
          error: (error as Error).message,
        };
      }
    })
  );

  const results = healthChecks.map(result => 
    result.status === 'fulfilled' ? result.value : {
      service: 'unknown',
      status: 'error',
      error: result.reason,
    }
  );

  const allHealthy = results.every(result => result.status === 'healthy');

  res.status(allHealthy ? 200 : 503).json({
    status: allHealthy ? 'healthy' : 'degraded',
    services: results,
    timestamp: new Date().toISOString(),
  });
});

// Authentication middleware for protected routes
const authMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  // Forward the token to the auth service for validation
  // The auth service will validate and return user info
  next();
};

// Proxy configuration with authentication forwarding
const createAuthProxy = (target: string, pathRewrite?: Record<string, string>) => {
  return createProxyMiddleware({
    target,
    changeOrigin: true,
    pathRewrite,
    onProxyReq: (proxyReq, req: any) => {
      // Forward authentication headers
      if (req.headers.authorization) {
        proxyReq.setHeader('authorization', req.headers.authorization);
      }
      
      // Forward user context if available
      if (req.user) {
        proxyReq.setHeader('x-user-id', req.user.userId);
        proxyReq.setHeader('x-user-roles', JSON.stringify(req.user.roles));
        proxyReq.setHeader('x-user-permissions', JSON.stringify(req.user.permissions));
      }
    },
    onError: (err, req, res) => {
      console.error('Proxy error:', err);
      (res as express.Response).status(503).json({
        error: 'Service temporarily unavailable',
        message: 'The requested service is currently unavailable. Please try again later.',
      });
    },
  });
};

// Route proxying
app.use('/auth', createAuthProxy(services.auth, { '^/auth': '' }));
app.use('/inventory', createAuthProxy(services.inventory, { '^/inventory': '' }));
app.use('/sales', createAuthProxy(services.sales, { '^/sales': '' }));
app.use('/orders', createAuthProxy(services.orders, { '^/orders': '' }));
app.use('/customers', createAuthProxy(services.customers, { '^/customers': '' }));
app.use('/reports', createAuthProxy(services.reports, { '^/reports': '' }));
app.use('/notifications', createAuthProxy(services.notifications, { '^/notifications': '' }));

// GraphQL proxy for inventory service
app.use('/graphql', createAuthProxy(services.inventory, { '^/graphql': '/graphql' }));

// API versioning
app.use('/v1/auth', createAuthProxy(services.auth, { '^/v1/auth': '' }));
app.use('/v1/inventory', createAuthProxy(services.inventory, { '^/v1/inventory': '' }));
app.use('/v1/sales', createAuthProxy(services.sales, { '^/v1/sales': '' }));
app.use('/v1/orders', createAuthProxy(services.orders, { '^/v1/orders': '' }));
app.use('/v1/customers', createAuthProxy(services.customers, { '^/v1/customers': '' }));
app.use('/v1/reports', createAuthProxy(services.reports, { '^/v1/reports': '' }));
app.use('/v1/notifications', createAuthProxy(services.notifications, { '^/v1/notifications': '' }));

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('API Gateway error:', err);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';
  
  res.status(statusCode).json({
    error: message,
    timestamp: new Date().toISOString(),
    path: req.path,
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The requested endpoint ${req.method} ${req.originalUrl} was not found.`,
    timestamp: new Date().toISOString(),
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 API Gateway running on port ${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`🔗 GraphQL Playground: http://localhost:${PORT}/graphql`);
  console.log('\n🔗 Available Services:');
  Object.entries(services).forEach(([name, url]) => {
    console.log(`   ${name}: ${url}`);
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});
