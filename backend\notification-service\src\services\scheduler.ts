import axios from 'axios';
import { prisma } from '@shop/shared';
import { sendEmail, sendPushNotification } from './notificationSender';

const INVENTORY_SERVICE_URL = process.env.INVENTORY_SERVICE_URL || 'http://localhost:3002';

export const scheduleNotifications = async (): Promise<void> => {
  try {
    // Get notifications that are scheduled for now or past
    const scheduledNotifications = await prisma.notification.findMany({
      where: {
        scheduledFor: {
          lte: new Date()
        },
        sentAt: null
      }
    });

    for (const notification of scheduledNotifications) {
      try {
        // Get recipients based on type
        let recipients: any[] = [];

        if (notification.recipientType === 'user' && notification.recipientId) {
          const user = await prisma.user.findUnique({
            where: { id: notification.recipientId },
            select: { id: true, email: true, firstName: true, lastName: true }
          });
          if (user) recipients = [user];
        } else if (notification.recipientType === 'role' && notification.recipientId) {
          const users = await prisma.user.findMany({
            where: {
              roles: {
                some: {
                  role: {
                    name: notification.recipientId
                  }
                }
              }
            },
            select: { id: true, email: true, firstName: true, lastName: true }
          });
          recipients = users;
        } else if (notification.recipientType === 'all') {
          const users = await prisma.user.findMany({
            where: { isActive: true },
            select: { id: true, email: true, firstName: true, lastName: true }
          });
          recipients = users;
        }

        // Send notifications to recipients
        for (const recipient of recipients) {
          // Send email if recipient has email
          if (recipient.email) {
            try {
              await sendEmail({
                to: recipient.email,
                subject: notification.title,
                text: notification.message,
                html: `
                  <h2>${notification.title}</h2>
                  <p>${notification.message}</p>
                  ${notification.data ? `<pre>${JSON.stringify(notification.data, null, 2)}</pre>` : ''}
                `
              });
            } catch (error) {
              console.error(`Failed to send email to ${recipient.email}:`, error);
            }
          }

          // Send push notification
          try {
            await sendPushNotification(recipient.id, {
              title: notification.title,
              body: notification.message,
              data: notification.data || {}
            });
          } catch (error) {
            console.error(`Failed to send push notification to ${recipient.id}:`, error);
          }
        }

        // Mark notification as sent
        await prisma.notification.update({
          where: { id: notification.id },
          data: { sentAt: new Date() }
        });

        console.log(`Scheduled notification sent: ${notification.title}`);
      } catch (error) {
        console.error(`Failed to process notification ${notification.id}:`, error);
      }
    }
  } catch (error) {
    console.error('Schedule notifications error:', error);
  }
};

export const checkLowStock = async (): Promise<void> => {
  try {
    // Get low stock items from inventory service
    const response = await axios.get(`${INVENTORY_SERVICE_URL}/stock/low`);
    const lowStockItems = response.data.data.lowStockItems;

    if (lowStockItems && lowStockItems.length > 0) {
      // Create notification for low stock
      const notification = await prisma.notification.create({
        data: {
          type: 'low_stock_alert',
          title: 'Low Stock Alert',
          message: `${lowStockItems.length} product(s) are running low on stock`,
          recipientType: 'role',
          recipientId: 'admin',
          priority: 'high',
          data: {
            lowStockItems: lowStockItems.map((item: any) => ({
              productId: item.product.id,
              productName: item.product.name,
              sku: item.product.sku,
              currentStock: item.quantity,
              minThreshold: item.minThreshold,
            }))
          }
        }
      });

      // Get admin users
      const adminUsers = await prisma.user.findMany({
        where: {
          roles: {
            some: {
              role: {
                name: 'admin'
              }
            }
          },
          isActive: true
        },
        select: { id: true, email: true, firstName: true, lastName: true }
      });

      // Send notifications to admins
      for (const admin of adminUsers) {
        if (admin.email) {
          try {
            const itemsList = lowStockItems.map((item: any) => 
              `- ${item.product.name} (${item.product.sku}): ${item.quantity} remaining (min: ${item.minThreshold})`
            ).join('\n');

            await sendEmail({
              to: admin.email,
              subject: 'Low Stock Alert - Immediate Attention Required',
              text: `
Low Stock Alert

The following products are running low on stock and need immediate attention:

${itemsList}

Please restock these items as soon as possible.

Best regards,
Shop Management System
              `,
              html: `
                <h2>🚨 Low Stock Alert</h2>
                <p>The following products are running low on stock and need immediate attention:</p>
                <ul>
                  ${lowStockItems.map((item: any) => `
                    <li>
                      <strong>${item.product.name}</strong> (${item.product.sku})<br>
                      Current Stock: <span style="color: red;">${item.quantity}</span> | 
                      Minimum Threshold: ${item.minThreshold}
                    </li>
                  `).join('')}
                </ul>
                <p>Please restock these items as soon as possible.</p>
                <p><em>Best regards,<br>Shop Management System</em></p>
              `
            });
          } catch (error) {
            console.error(`Failed to send low stock email to ${admin.email}:`, error);
          }
        }

        // Send push notification
        try {
          await sendPushNotification(admin.id, {
            title: '🚨 Low Stock Alert',
            body: `${lowStockItems.length} product(s) are running low on stock`,
            data: {
              type: 'low_stock_alert',
              count: lowStockItems.length,
              items: lowStockItems.slice(0, 5) // Send first 5 items in push data
            }
          });
        } catch (error) {
          console.error(`Failed to send low stock push notification to ${admin.id}:`, error);
        }
      }

      console.log(`Low stock alert sent for ${lowStockItems.length} items`);
    }
  } catch (error) {
    console.error('Low stock check error:', error);
  }
};

export const sendOrderReminders = async (): Promise<void> => {
  try {
    // Get orders that are ready for pickup but not completed
    const readyOrders = await prisma.order.findMany({
      where: {
        status: 'ready',
        createdAt: {
          lte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
        }
      },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          }
        }
      }
    });

    for (const order of readyOrders) {
      if (order.customer.email) {
        try {
          await sendEmail({
            to: order.customer.email,
            subject: 'Order Ready for Pickup - Reminder',
            text: `
Dear ${order.customer.firstName} ${order.customer.lastName},

Your order ${order.orderNumber} is ready for pickup.

Please visit our store at your earliest convenience to collect your order.

Thank you for your business!

Best regards,
Shop Team
            `,
            html: `
              <h2>Order Ready for Pickup</h2>
              <p>Dear ${order.customer.firstName} ${order.customer.lastName},</p>
              <p>Your order <strong>${order.orderNumber}</strong> is ready for pickup.</p>
              <p>Please visit our store at your earliest convenience to collect your order.</p>
              <p>Thank you for your business!</p>
              <p><em>Best regards,<br>Shop Team</em></p>
            `
          });

          console.log(`Pickup reminder sent for order ${order.orderNumber}`);
        } catch (error) {
          console.error(`Failed to send pickup reminder for order ${order.orderNumber}:`, error);
        }
      }
    }
  } catch (error) {
    console.error('Order reminders error:', error);
  }
};

export default {
  scheduleNotifications,
  checkLowStock,
  sendOrderReminders,
};
