const crypto = require('crypto');

// Generate unique identifiers
const generateSaleNumber = () => {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(4).toString('hex');
  return `SALE-${timestamp}-${random}`.toUpperCase();
};

const generateOrderNumber = () => {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(4).toString('hex');
  return `ORD-${timestamp}-${random}`.toUpperCase();
};

const generateSKU = (categoryName, productName) => {
  const catCode = categoryName.substring(0, 3).toUpperCase();
  const prodCode = productName.substring(0, 3).toUpperCase();
  const random = crypto.randomBytes(2).toString('hex').toUpperCase();
  return `${catCode}-${prodCode}-${random}`;
};

// Date utilities
const formatDate = (date) => {
  return new Date(date).toISOString().split('T')[0];
};

const formatDateTime = (date) => {
  return new Date(date).toISOString();
};

const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

const isToday = (date) => {
  const today = new Date();
  const checkDate = new Date(date);
  return today.toDateString() === checkDate.toDateString();
};

// Number utilities
const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

const calculateTax = (amount, taxRate = 0.1) => {
  return Math.round(amount * taxRate * 100) / 100;
};

const calculateDiscount = (amount, discountPercent) => {
  return Math.round(amount * (discountPercent / 100) * 100) / 100;
};

// String utilities
const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
};

const capitalize = (text) => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

const truncate = (text, length = 100) => {
  if (text.length <= length) return text;
  return text.substring(0, length) + '...';
};

// Array utilities
const paginate = (array, page = 1, limit = 10) => {
  const offset = (page - 1) * limit;
  const paginatedItems = array.slice(offset, offset + limit);
  
  return {
    data: paginatedItems,
    pagination: {
      page,
      limit,
      total: array.length,
      totalPages: Math.ceil(array.length / limit),
      hasNext: offset + limit < array.length,
      hasPrev: page > 1,
    },
  };
};

const groupBy = (array, key) => {
  return array.reduce((groups, item) => {
    const group = item[key];
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {});
};

// Object utilities
const pick = (obj, keys) => {
  return keys.reduce((result, key) => {
    if (obj[key] !== undefined) {
      result[key] = obj[key];
    }
    return result;
  }, {});
};

const omit = (obj, keys) => {
  const result = { ...obj };
  keys.forEach(key => delete result[key]);
  return result;
};

// Error handling utilities
const createError = (message, statusCode = 500, code = null) => {
  const error = new Error(message);
  error.statusCode = statusCode;
  error.code = code;
  return error;
};

const handleAsyncError = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Response utilities
const sendSuccess = (res, data = null, message = 'Success', statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
    timestamp: new Date().toISOString(),
  });
};

const sendError = (res, message = 'Internal Server Error', statusCode = 500, errors = null) => {
  return res.status(statusCode).json({
    success: false,
    message,
    errors,
    timestamp: new Date().toISOString(),
  });
};

// Validation utilities
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const isValidPhone = (phone) => {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
  return phoneRegex.test(phone);
};

const isValidURL = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Cache utilities
const getCacheKey = (...parts) => {
  return parts.join(':');
};

const setCacheWithTTL = async (redisClient, key, value, ttlSeconds = 3600) => {
  await redisClient.setEx(key, ttlSeconds, JSON.stringify(value));
};

const getCacheValue = async (redisClient, key) => {
  const value = await redisClient.get(key);
  return value ? JSON.parse(value) : null;
};

module.exports = {
  // ID generators
  generateSaleNumber,
  generateOrderNumber,
  generateSKU,
  
  // Date utilities
  formatDate,
  formatDateTime,
  addDays,
  isToday,
  
  // Number utilities
  formatCurrency,
  calculateTax,
  calculateDiscount,
  
  // String utilities
  slugify,
  capitalize,
  truncate,
  
  // Array utilities
  paginate,
  groupBy,
  
  // Object utilities
  pick,
  omit,
  
  // Error handling
  createError,
  handleAsyncError,
  
  // Response utilities
  sendSuccess,
  sendError,
  
  // Validation utilities
  isValidEmail,
  isValidPhone,
  isValidURL,
  
  // Cache utilities
  getCacheKey,
  setCacheWithTTL,
  getCacheValue,
};
