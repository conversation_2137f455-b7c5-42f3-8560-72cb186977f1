#!/bin/bash

echo "🏬 Setting up Shop Software System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version check passed: $(node -v)"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_warning "Docker is not installed. You'll need to set up PostgreSQL and Redis manually."
else
    print_status "Docker is available"
fi

# Create environment files
print_info "Creating environment files..."

# Root .env file
cat > .env << EOF
# Database
DATABASE_URL="postgresql://shop_user:shop_password@localhost:5432/shop_db"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="15m"
REFRESH_TOKEN_EXPIRES_IN="7d"

# Services
API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
INVENTORY_SERVICE_PORT=3002
SALES_SERVICE_PORT=3003
ORDER_SERVICE_PORT=3004
CUSTOMER_SERVICE_PORT=3005
REPORTING_SERVICE_PORT=3006
NOTIFICATION_SERVICE_PORT=3007

# Frontend
FRONTEND_PORT=4200
API_BASE_URL="http://localhost:3000"

# CORS
ALLOWED_ORIGINS="http://localhost:4200,http://localhost:8100"

# Environment
NODE_ENV="development"
EOF

print_status "Environment file created"

# Install root dependencies
print_info "Installing root dependencies..."
npm install

# Install backend dependencies
print_info "Installing backend dependencies..."

# Shared dependencies
cd backend/shared
npm install
print_status "Shared dependencies installed"
cd ../..

# API Gateway
cd backend/api-gateway
npm install
print_status "API Gateway dependencies installed"
cd ../..

# Auth Service
cd backend/auth-service
npm install
print_status "Auth Service dependencies installed"
cd ../..

# Inventory Service
cd backend/inventory-service
npm install
print_status "Inventory Service dependencies installed"
cd ../..

# Check if Docker is available and start services
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    print_info "Starting database services with Docker..."
    
    # Create docker-compose override for development
    cat > docker-compose.override.yml << EOF
version: '3.8'

services:
  postgres:
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
EOF

    # Start only database services
    docker-compose up -d postgres redis
    
    print_status "Database services started"
    
    # Wait for PostgreSQL to be ready
    print_info "Waiting for PostgreSQL to be ready..."
    sleep 10
    
    # Generate Prisma client and run migrations
    print_info "Setting up database..."
    cd backend/shared
    npx prisma generate
    npx prisma migrate dev --name init
    npx prisma db seed
    cd ../..
    
    print_status "Database setup completed"
else
    print_warning "Docker not available. Please set up PostgreSQL and Redis manually:"
    print_info "PostgreSQL: Create database 'shop_db' with user 'shop_user' and password 'shop_password'"
    print_info "Redis: Start Redis server on port 6379"
    print_info "Then run: cd backend/shared && npx prisma generate && npx prisma migrate dev && npx prisma db seed"
fi

# Create start scripts
print_info "Creating start scripts..."

# Development start script
cat > start-dev.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting Shop Software System in development mode..."

# Start backend services
echo "Starting backend services..."
npm run dev &

# Wait a bit for services to start
sleep 5

echo "✅ All services started!"
echo ""
echo "🔗 Available endpoints:"
echo "   API Gateway: http://localhost:3000"
echo "   API Documentation: http://localhost:3000/api-docs"
echo "   Health Check: http://localhost:3000/health"
echo ""
echo "🔐 Default credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   Worker: <EMAIL> / worker123"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user interrupt
wait
EOF

chmod +x start-dev.sh

# Production start script
cat > start-prod.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting Shop Software System in production mode..."

# Build and start with Docker
docker-compose up --build -d

echo "✅ All services started in production mode!"
echo ""
echo "🔗 Available endpoints:"
echo "   API Gateway: http://localhost:3000"
echo "   Frontend: http://localhost:4200"
echo ""
echo "To stop: docker-compose down"
EOF

chmod +x start-prod.sh

print_status "Start scripts created"

# Create README for quick start
cat > QUICK_START.md << 'EOF'
# Quick Start Guide

## Prerequisites
- Node.js 18+
- Docker & Docker Compose (recommended)
- PostgreSQL & Redis (if not using Docker)

## Development Setup

1. **Start the system:**
   ```bash
   ./start-dev.sh
   ```

2. **Access the application:**
   - API Gateway: http://localhost:3000
   - API Documentation: http://localhost:3000/api-docs
   - Health Check: http://localhost:3000/health

3. **Default credentials:**
   - Admin: <EMAIL> / admin123
   - Worker: <EMAIL> / worker123

## Production Setup

1. **Start with Docker:**
   ```bash
   ./start-prod.sh
   ```

## Manual Database Setup (if not using Docker)

1. **Install dependencies:**
   ```bash
   npm run install:all
   ```

2. **Setup database:**
   ```bash
   cd backend/shared
   npx prisma generate
   npx prisma migrate dev
   npx prisma db seed
   ```

3. **Start services:**
   ```bash
   npm run dev
   ```

## Available Scripts

- `npm run dev` - Start all services in development mode
- `npm run build` - Build all services
- `npm run test` - Run tests
- `npm run docker:up` - Start with Docker
- `npm run docker:down` - Stop Docker services

## Service Endpoints

- API Gateway: :3000
- Auth Service: :3001
- Inventory Service: :3002
- Sales Service: :3003
- Order Service: :3004
- Customer Service: :3005
- Reporting Service: :3006
- Notification Service: :3007

## GraphQL

GraphQL endpoint available at: http://localhost:3000/graphql

## Database Management

- Prisma Studio: `cd backend/shared && npx prisma studio`
- Reset database: `cd backend/shared && npx prisma migrate reset`
- Generate client: `cd backend/shared && npx prisma generate`
EOF

print_status "Quick start guide created"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
print_info "Next steps:"
echo "1. Review the .env file and update any necessary configurations"
echo "2. Run './start-dev.sh' to start the development environment"
echo "3. Visit http://localhost:3000/api-docs for API documentation"
echo "4. Check QUICK_START.md for detailed instructions"
echo ""
print_info "Default credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   Worker: <EMAIL> / worker123"
echo ""
print_warning "Remember to change the JWT_SECRET in production!"
EOF
