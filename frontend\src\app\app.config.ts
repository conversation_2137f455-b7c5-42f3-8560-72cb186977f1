import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideServiceWorker } from '@angular/service-worker';
import { provideStore } from '@ngrx/store';
import { provideEffects } from '@ngrx/effects';
import { provideStoreDevtools } from '@ngrx/store-devtools';

import { routes } from './app.routes';
import { environment } from '@environments/environment';
import { authInterceptor } from '@core/interceptors/auth.interceptor';
import { errorInterceptor } from '@core/interceptors/error.interceptor';
import { loadingInterceptor } from '@core/interceptors/loading.interceptor';
import { reducers, metaReducers } from '@core/store';
import { AuthEffects } from '@core/store/auth/auth.effects';
import { InventoryEffects } from '@core/store/inventory/inventory.effects';
import { SalesEffects } from '@core/store/sales/sales.effects';
import { OrderEffects } from '@core/store/orders/order.effects';
import { CustomerEffects } from '@core/store/customers/customer.effects';
import { NotificationEffects } from '@core/store/notifications/notification.effects';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(
      withInterceptors([
        authInterceptor,
        errorInterceptor,
        loadingInterceptor
      ])
    ),
    provideAnimations(),
    provideServiceWorker('ngsw-worker.js', {
      enabled: environment.production,
      registrationStrategy: 'registerWhenStable:30000'
    }),
    provideStore(reducers, { metaReducers }),
    provideEffects([
      AuthEffects,
      InventoryEffects,
      SalesEffects,
      OrderEffects,
      CustomerEffects,
      NotificationEffects
    ]),
    provideStoreDevtools({
      maxAge: 25,
      logOnly: environment.production,
      autoPause: true,
      trace: false,
      traceLimit: 75
    })
  ]
};
