// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Auth Service Models
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  password    String
  firstName   String
  lastName    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  roles       UserRole[]
  sessionLogs SessionLog[]
  shifts      Shift[]
  sales       Sale[]
  
  @@map("users")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique // "admin", "worker"
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  users       UserRole[]
  permissions RolePermission[]
  
  @@map("roles")
}

model Permission {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  resource    String   // "inventory", "sales", "orders", etc.
  action      String   // "create", "read", "update", "delete"
  createdAt   DateTime @default(now())
  
  // Relations
  roles       RolePermission[]
  
  @@map("permissions")
}

model UserRole {
  id     String @id @default(cuid())
  userId String
  roleId String
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)
  
  @@unique([userId, roleId])
  @@map("user_roles")
}

model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String
  
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model SessionLog {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  isActive  Boolean  @default(true)
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("session_logs")
}

model Shift {
  id        String    @id @default(cuid())
  userId    String
  startTime DateTime
  endTime   DateTime?
  status    String    @default("active") // "active", "completed"
  notes     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("shifts")
}

// Inventory Service Models
model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  products Product[]
  
  @@map("categories")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  sku         String   @unique
  barcode     String?  @unique
  price       Decimal  @db.Decimal(10, 2)
  cost        Decimal? @db.Decimal(10, 2)
  categoryId  String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  category  Category    @relation(fields: [categoryId], references: [id])
  stock     Stock?
  saleItems SaleItem[]
  orderItems OrderItem[]
  
  @@map("products")
}

model Stock {
  id              String   @id @default(cuid())
  productId       String   @unique
  quantity        Int      @default(0)
  minThreshold    Int      @default(10)
  maxThreshold    Int?
  lastRestocked   DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@map("stock")
}

// Sales Service Models
model Sale {
  id          String   @id @default(cuid())
  saleNumber  String   @unique
  userId      String
  customerId  String?
  subtotal    Decimal  @db.Decimal(10, 2)
  tax         Decimal  @db.Decimal(10, 2) @default(0)
  discount    Decimal  @db.Decimal(10, 2) @default(0)
  total       Decimal  @db.Decimal(10, 2)
  status      String   @default("completed") // "pending", "completed", "cancelled"
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  user      User       @relation(fields: [userId], references: [id])
  customer  Customer?  @relation(fields: [customerId], references: [id])
  items     SaleItem[]
  payments  Payment[]
  receipt   Receipt?
  
  @@map("sales")
}

model SaleItem {
  id        String  @id @default(cuid())
  saleId    String
  productId String
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)
  
  sale    Sale    @relation(fields: [saleId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
  
  @@map("sale_items")
}

model Payment {
  id            String   @id @default(cuid())
  saleId        String
  method        String   // "cash", "card", "digital"
  amount        Decimal  @db.Decimal(10, 2)
  reference     String?
  status        String   @default("completed") // "pending", "completed", "failed"
  processedAt   DateTime @default(now())
  
  sale Sale @relation(fields: [saleId], references: [id], onDelete: Cascade)
  
  @@map("payments")
}

model Receipt {
  id          String   @id @default(cuid())
  saleId      String   @unique
  receiptData Json
  printedAt   DateTime?
  createdAt   DateTime @default(now())
  
  sale Sale @relation(fields: [saleId], references: [id], onDelete: Cascade)
  
  @@map("receipts")
}

// Customer Service Models
model Customer {
  id          String   @id @default(cuid())
  firstName   String
  lastName    String
  email       String?  @unique
  phone       String?
  address     String?
  city        String?
  postalCode  String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  contacts      Contact[]
  notes         CustomerNote[]
  sales         Sale[]
  orders        Order[]
  
  @@map("customers")
}

model Contact {
  id         String @id @default(cuid())
  customerId String
  type       String // "email", "phone", "address"
  value      String
  isPrimary  Boolean @default(false)
  
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("contacts")
}

model CustomerNote {
  id         String   @id @default(cuid())
  customerId String
  note       String
  createdBy  String
  createdAt  DateTime @default(now())
  
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("customer_notes")
}

// Order Management Service Models
model Order {
  id          String   @id @default(cuid())
  orderNumber String   @unique
  customerId  String
  status      String   @default("pending") // "pending", "processing", "ready", "completed", "cancelled"
  subtotal    Decimal  @db.Decimal(10, 2)
  tax         Decimal  @db.Decimal(10, 2) @default(0)
  total       Decimal  @db.Decimal(10, 2)
  notes       String?
  expectedDate DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  customer    Customer      @relation(fields: [customerId], references: [id])
  items       OrderItem[]
  statusLogs  OrderStatus[]
  orderNotes  OrderNote[]
  
  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)
  
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
  
  @@map("order_items")
}

model OrderStatus {
  id        String   @id @default(cuid())
  orderId   String
  status    String
  notes     String?
  createdBy String
  createdAt DateTime @default(now())
  
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  @@map("order_status")
}

model OrderNote {
  id        String   @id @default(cuid())
  orderId   String
  note      String
  createdBy String
  createdAt DateTime @default(now())
  
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  @@map("order_notes")
}

// Notification Service Models
model Notification {
  id            String   @id @default(cuid())
  type          String   // "low_stock", "order_ready", "shift_reminder", "sales_target"
  title         String
  message       String
  recipientId   String?
  recipientType String   @default("user") // "user", "role", "all"
  priority      String   @default("normal") // "low", "normal", "high", "urgent"
  isRead        Boolean  @default(false)
  data          Json?
  scheduledFor  DateTime?
  createdAt     DateTime @default(now())
  
  // Relations
  deliveryStatus DeliveryStatus[]
  
  @@map("notifications")
}

model DeliveryStatus {
  id             String   @id @default(cuid())
  notificationId String
  channel        String   // "push", "email", "sms", "in_app"
  status         String   // "pending", "sent", "delivered", "failed"
  attemptCount   Int      @default(0)
  lastAttempt    DateTime?
  deliveredAt    DateTime?
  errorMessage   String?
  
  notification Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  
  @@map("delivery_status")
}
