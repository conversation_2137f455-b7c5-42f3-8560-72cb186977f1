require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');

const { auth, redis, utils } = require('@shop/shared');

const app = express();
const PORT = process.env.PORT || 3000;

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Shop Software API',
      version: '1.0.0',
      description: 'Complete microservices-based shop software system API',
    },
    servers: [
      {
        url: `http://localhost:${PORT}`,
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  apis: ['./src/routes/*.js', './src/swagger/*.yaml'],
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:4200', 'http://localhost:8100'],
  credentials: true,
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: (req) => {
    // Higher limits for authenticated users
    if (req.user) {
      return 1000; // 1000 requests per 15 minutes for authenticated users
    }
    return 100; // 100 requests per 15 minutes for anonymous users
  },
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Service registry
const services = {
  auth: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
  inventory: process.env.INVENTORY_SERVICE_URL || 'http://localhost:3002',
  sales: process.env.SALES_SERVICE_URL || 'http://localhost:3003',
  orders: process.env.ORDER_SERVICE_URL || 'http://localhost:3004',
  customers: process.env.CUSTOMER_SERVICE_URL || 'http://localhost:3005',
  reporting: process.env.REPORTING_SERVICE_URL || 'http://localhost:3006',
  notifications: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3007',
};

// Authentication middleware for protected routes
const authenticateRequest = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const decoded = auth.verifyToken(token);
    
    // Add user info to request headers for downstream services
    req.headers['x-user-id'] = decoded.userId;
    req.headers['x-user-roles'] = JSON.stringify(decoded.roles || []);
    req.headers['x-user-permissions'] = JSON.stringify(decoded.permissions || []);
    
    next();
  } catch (error) {
    // Don't block request, let downstream service handle auth
    next();
  }
};

// Proxy configuration
const createProxy = (target, pathRewrite = {}) => {
  return createProxyMiddleware({
    target,
    changeOrigin: true,
    pathRewrite,
    onProxyReq: (proxyReq, req, res) => {
      // Add request ID for tracing
      const requestId = require('crypto').randomUUID();
      proxyReq.setHeader('x-request-id', requestId);
      
      // Forward user context
      if (req.headers['x-user-id']) {
        proxyReq.setHeader('x-user-id', req.headers['x-user-id']);
        proxyReq.setHeader('x-user-roles', req.headers['x-user-roles']);
        proxyReq.setHeader('x-user-permissions', req.headers['x-user-permissions']);
      }
    },
    onError: (err, req, res) => {
      console.error('Proxy error:', err.message);
      res.status(503).json({
        error: 'Service temporarily unavailable',
        message: 'The requested service is currently unavailable. Please try again later.',
      });
    },
  });
};

// Apply authentication middleware to all API routes
app.use('/api', authenticateRequest);

// Route proxying
app.use('/api/auth', createProxy(services.auth, { '^/api/auth': '' }));
app.use('/api/inventory', createProxy(services.inventory, { '^/api/inventory': '' }));
app.use('/api/sales', createProxy(services.sales, { '^/api/sales': '' }));
app.use('/api/orders', createProxy(services.orders, { '^/api/orders': '' }));
app.use('/api/customers', createProxy(services.customers, { '^/api/customers': '' }));
app.use('/api/reporting', createProxy(services.reporting, { '^/api/reporting': '' }));
app.use('/api/notifications', createProxy(services.notifications, { '^/api/notifications': '' }));

// GraphQL endpoint (if needed)
app.use('/graphql', createProxy(services.inventory, { '^/graphql': '/graphql' }));

// Catch-all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The requested endpoint ${req.originalUrl} was not found.`,
    availableEndpoints: [
      '/health',
      '/api-docs',
      '/api/auth',
      '/api/inventory',
      '/api/sales',
      '/api/orders',
      '/api/customers',
      '/api/reporting',
      '/api/notifications',
    ],
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Gateway error:', err);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';
  
  res.status(statusCode).json({
    error: message,
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'],
  });
});

// Initialize Redis connection
const initializeServices = async () => {
  try {
    await redis.connectRedis();
    console.log('✅ Redis connected');
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    console.log('⚠️  Continuing without Redis cache');
  }
};

// Start server
const startServer = async () => {
  await initializeServices();
  
  app.listen(PORT, () => {
    console.log(`🚀 API Gateway running on port ${PORT}`);
    console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
    console.log('\n🔗 Service Registry:');
    Object.entries(services).forEach(([name, url]) => {
      console.log(`   ${name}: ${url}`);
    });
  });
};

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  await redis.disconnectRedis();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  await redis.disconnectRedis();
  process.exit(0);
});

startServer().catch((error) => {
  console.error('❌ Failed to start API Gateway:', error);
  process.exit(1);
});
