{"name": "@shop/sales-service", "version": "1.0.0", "description": "Sales processing service for shop software system", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step required'", "test": "jest"}, "dependencies": {"@shop/shared": "file:../shared", "express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "axios": "^1.6.2", "pdfkit": "^0.13.0", "qrcode": "^1.5.3", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}