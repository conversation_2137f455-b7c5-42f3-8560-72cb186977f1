version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15
    container_name: shop_postgres
    environment:
      POSTGRES_DB: shop_db
      POSTGRES_USER: shop_user
      POSTGRES_PASSWORD: shop_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - shop_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: shop_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - shop_network

  # API Gateway
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile
    container_name: shop_api_gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Auth Service
  auth-service:
    build:
      context: ./backend/auth-service
      dockerfile: Dockerfile
    container_name: shop_auth_service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Inventory Service
  inventory-service:
    build:
      context: ./backend/inventory-service
      dockerfile: Dockerfile
    container_name: shop_inventory_service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Sales Service
  sales-service:
    build:
      context: ./backend/sales-service
      dockerfile: Dockerfile
    container_name: shop_sales_service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Order Service
  order-service:
    build:
      context: ./backend/order-service
      dockerfile: Dockerfile
    container_name: shop_order_service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Customer Service
  customer-service:
    build:
      context: ./backend/customer-service
      dockerfile: Dockerfile
    container_name: shop_customer_service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Reporting Service
  reporting-service:
    build:
      context: ./backend/reporting-service
      dockerfile: Dockerfile
    container_name: shop_reporting_service
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Notification Service
  notification-service:
    build:
      context: ./backend/notification-service
      dockerfile: Dockerfile
    container_name: shop_notification_service
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=development
      - PORT=3007
      - DATABASE_URL=**************************************************/shop_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - shop_network

  # Frontend (Angular PWA)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: shop_frontend
    ports:
      - "4200:4200"
    environment:
      - NODE_ENV=development
      - API_BASE_URL=http://api-gateway:3000
    depends_on:
      - api-gateway
    networks:
      - shop_network

volumes:
  postgres_data:
  redis_data:

networks:
  shop_network:
    driver: bridge
