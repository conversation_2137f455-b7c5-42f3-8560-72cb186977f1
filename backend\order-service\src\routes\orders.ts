import express from 'express';
import axios from 'axios';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

// Service URLs
const INVENTORY_SERVICE_URL = process.env.INVENTORY_SERVICE_URL || 'http://localhost:3002';
const NOTIFICATION_SERVICE_URL = process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3007';

/**
 * @swagger
 * /orders:
 *   get:
 *     summary: Get all orders
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, ready, completed, cancelled]
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 */
router.get('/', requireAuth, requirePermission('orders:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { page, limit, status, customerId } = req.query as any;
  const skip = (page - 1) * limit;

  const where: any = {};
  if (status) where.status = status;
  if (customerId) where.customerId = customerId;

  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where,
      skip,
      take: limit,
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                price: true,
              }
            }
          }
        },
        statusLogs: {
          orderBy: { createdAt: 'desc' },
          take: 1
        },
        _count: {
          select: {
            orderNotes: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.order.count({ where })
  ]);

  const ordersResponse = orders.map(order => ({
    ...order,
    notesCount: order._count.orderNotes,
    latestStatus: order.statusLogs[0] || null,
  }));

  utils.sendSuccess(res, {
    orders: ordersResponse,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: skip + limit < total,
      hasPrev: page > 1,
    }
  });
}));

/**
 * @swagger
 * /orders/{id}:
 *   get:
 *     summary: Get order by ID
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order retrieved successfully
 *       404:
 *         description: Order not found
 */
router.get('/:id', requireAuth, requirePermission('orders:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { id } = req.params;

  const order = await prisma.order.findUnique({
    where: { id },
    include: {
      customer: true,
      items: {
        include: {
          product: true
        }
      },
      statusLogs: {
        orderBy: { createdAt: 'desc' }
      },
      orderNotes: {
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  if (!order) {
    utils.sendError(res, 'Order not found', 404);
    return;
  }

  utils.sendSuccess(res, { order });
}));

/**
 * @swagger
 * /orders:
 *   post:
 *     summary: Create new order
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - items
 *             properties:
 *               customerId:
 *                 type: string
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - productId
 *                     - quantity
 *                     - unitPrice
 *                   properties:
 *                     productId:
 *                       type: string
 *                     quantity:
 *                       type: integer
 *                     unitPrice:
 *                       type: number
 *               notes:
 *                 type: string
 *               expectedDate:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       201:
 *         description: Order created successfully
 */
router.post('/', requireAuth, requirePermission('orders:create'), validation.validateBody(validation.orderCreateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { customerId, items, notes, expectedDate } = req.body;
  const userId = req.user!.userId;

  // Validate customer exists
  const customer = await prisma.customer.findUnique({
    where: { id: customerId }
  });

  if (!customer) {
    utils.sendError(res, 'Customer not found', 404);
    return;
  }

  // Calculate totals
  const subtotal = items.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0);
  const tax = utils.calculateTax(subtotal);
  const total = subtotal + tax;

  // Generate order number
  const orderNumber = utils.generateOrderNumber();

  try {
    // Create order with transaction
    const order = await prisma.$transaction(async (tx) => {
      // Create order
      const newOrder = await tx.order.create({
        data: {
          orderNumber,
          customerId,
          subtotal,
          tax,
          total,
          notes,
          expectedDate: expectedDate ? new Date(expectedDate) : null,
          status: 'pending',
        }
      });

      // Create order items
      const orderItems = await Promise.all(
        items.map((item: any) =>
          tx.orderItem.create({
            data: {
              orderId: newOrder.id,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              total: item.quantity * item.unitPrice,
            }
          })
        )
      );

      // Create initial status log
      await tx.orderStatus.create({
        data: {
          orderId: newOrder.id,
          status: 'pending',
          notes: 'Order created',
          createdBy: userId,
        }
      });

      return { ...newOrder, items: orderItems };
    });

    // Send notification (optional - don't fail if notification service is down)
    try {
      await axios.post(`${NOTIFICATION_SERVICE_URL}/notifications`, {
        type: 'order_created',
        title: 'New Order Created',
        message: `Order ${orderNumber} has been created for ${customer.firstName} ${customer.lastName}`,
        recipientType: 'role',
        recipientId: 'admin',
        priority: 'normal',
        data: {
          orderId: order.id,
          orderNumber,
          customerId,
          total,
        }
      }, {
        headers: {
          'x-user-id': req.user!.userId,
          'x-user-roles': JSON.stringify(req.user!.roles),
          'x-user-permissions': JSON.stringify(req.user!.permissions),
        }
      });
    } catch (error) {
      console.warn('Failed to send notification:', (error as Error).message);
    }

    // Fetch complete order data
    const completeOrder = await prisma.order.findUnique({
      where: { id: order.id },
      include: {
        customer: true,
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                price: true,
              }
            }
          }
        },
        statusLogs: {
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    utils.sendSuccess(res, { order: completeOrder }, 'Order created successfully', 201);
  } catch (error) {
    console.error('Order creation failed:', error);
    utils.sendError(res, 'Failed to create order', 500);
  }
}));

/**
 * @swagger
 * /orders/{id}/status:
 *   put:
 *     summary: Update order status
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [pending, processing, ready, completed, cancelled]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Order status updated successfully
 */
router.put('/:id/status', requireAuth, requirePermission('orders:update'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { id } = req.params;
  const { status, notes } = req.body;
  const userId = req.user!.userId;

  const order = await prisma.order.findUnique({
    where: { id },
    include: {
      customer: true
    }
  });

  if (!order) {
    utils.sendError(res, 'Order not found', 404);
    return;
  }

  if (order.status === status) {
    utils.sendError(res, `Order is already ${status}`, 400);
    return;
  }

  try {
    // Update order status and create status log
    const updatedOrder = await prisma.$transaction(async (tx) => {
      // Update order
      const updated = await tx.order.update({
        where: { id },
        data: {
          status,
          completedAt: status === 'completed' ? new Date() : null,
        },
        include: {
          customer: true,
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  price: true,
                }
              }
            }
          },
          statusLogs: {
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      // Create status log
      await tx.orderStatus.create({
        data: {
          orderId: id,
          status,
          notes: notes || `Order status changed to ${status}`,
          createdBy: userId,
        }
      });

      return updated;
    });

    // Send notification for status changes
    try {
      const statusMessages: Record<string, string> = {
        processing: 'Your order is now being processed',
        ready: 'Your order is ready for pickup',
        completed: 'Your order has been completed',
        cancelled: 'Your order has been cancelled',
      };

      if (statusMessages[status]) {
        await axios.post(`${NOTIFICATION_SERVICE_URL}/notifications`, {
          type: 'order_status_update',
          title: 'Order Status Update',
          message: statusMessages[status],
          recipientType: 'user',
          recipientId: order.customer.id,
          priority: status === 'ready' ? 'high' : 'normal',
          data: {
            orderId: id,
            orderNumber: order.orderNumber,
            status,
            notes,
          }
        }, {
          headers: {
            'x-user-id': req.user!.userId,
            'x-user-roles': JSON.stringify(req.user!.roles),
            'x-user-permissions': JSON.stringify(req.user!.permissions),
          }
        });
      }
    } catch (error) {
      console.warn('Failed to send status notification:', (error as Error).message);
    }

    utils.sendSuccess(res, { order: updatedOrder }, 'Order status updated successfully');
  } catch (error) {
    console.error('Order status update failed:', error);
    utils.sendError(res, 'Failed to update order status', 500);
  }
}));

export default router;
