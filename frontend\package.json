{"name": "@shop/frontend", "version": "1.0.0", "description": "Angular frontend for shop software system", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "e2e": "ng e2e", "lint": "ng lint", "serve:pwa": "ng build --configuration production && http-server -p 4200 -c-1 dist/frontend"}, "dependencies": {"@angular/animations": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/material": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/pwa": "^17.0.0", "@angular/router": "^17.0.0", "@angular/service-worker": "^17.0.0", "@ngrx/effects": "^17.0.0", "@ngrx/entity": "^17.0.0", "@ngrx/store": "^17.0.0", "@ngrx/store-devtools": "^17.0.0", "chart.js": "^4.4.0", "ng2-charts": "^5.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-headless": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0", "http-server": "^14.1.1"}}