{"name": "@shop/inventory-service", "version": "1.0.0", "description": "Inventory management service for shop software system", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step required'", "test": "jest"}, "dependencies": {"@shop/shared": "file:../shared", "express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "apollo-server-express": "^3.12.1", "graphql": "^16.8.1", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}