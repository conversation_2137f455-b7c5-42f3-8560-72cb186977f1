{"name": "@shop/auth-service", "version": "1.0.0", "description": "Authentication service for shop software system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@shop/shared": "file:../shared", "express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}