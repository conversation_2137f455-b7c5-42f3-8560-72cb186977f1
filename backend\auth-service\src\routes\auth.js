const express = require('express');
const { prisma, auth, validation, utils } = require('@shop/shared');

const router = express.Router();

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', validation.validateBody(auth.loginSchema), utils.handleAsyncError(async (req, res) => {
  const { email, password } = req.body;

  // Find user with roles and permissions
  const user = await prisma.user.findUnique({
    where: { email },
    include: {
      roles: {
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      }
    }
  });

  if (!user || !user.isActive) {
    return utils.sendError(res, 'Invalid credentials', 401);
  }

  const isValidPassword = await auth.comparePassword(password, user.password);
  if (!isValidPassword) {
    return utils.sendError(res, 'Invalid credentials', 401);
  }

  // Extract roles and permissions
  const roles = user.roles.map(ur => ur.role.name);
  const permissions = user.roles.flatMap(ur => 
    ur.role.permissions.map(rp => rp.permission.name)
  );

  // Generate tokens
  const tokenPayload = {
    userId: user.id,
    email: user.email,
    username: user.username,
    roles,
    permissions,
  };

  const accessToken = auth.generateAccessToken(tokenPayload);
  const refreshToken = auth.generateRefreshToken({ userId: user.id });

  // Store session
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  await prisma.sessionLog.create({
    data: {
      userId: user.id,
      token: refreshToken,
      expiresAt,
    }
  });

  // Remove password from response
  const { password: _, ...userResponse } = user;

  utils.sendSuccess(res, {
    user: {
      ...userResponse,
      roles,
      permissions,
    },
    tokens: {
      accessToken,
      refreshToken,
    }
  }, 'Login successful');
}));

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: User registration
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - username
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *                 minLength: 3
 *               password:
 *                 type: string
 *                 minLength: 6
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: User already exists
 */
router.post('/register', validation.validateBody(auth.registerSchema), utils.handleAsyncError(async (req, res) => {
  const { email, username, password, firstName, lastName } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { email },
        { username }
      ]
    }
  });

  if (existingUser) {
    return utils.sendError(res, 'User with this email or username already exists', 400);
  }

  // Hash password
  const hashedPassword = await auth.hashPassword(password);

  // Create user
  const user = await prisma.user.create({
    data: {
      email,
      username,
      password: hashedPassword,
      firstName,
      lastName,
    }
  });

  // Assign default worker role
  const workerRole = await prisma.role.findUnique({
    where: { name: 'worker' }
  });

  if (workerRole) {
    await prisma.userRole.create({
      data: {
        userId: user.id,
        roleId: workerRole.id,
      }
    });
  }

  // Remove password from response
  const { password: _, ...userResponse } = user;

  utils.sendSuccess(res, { user: userResponse }, 'User registered successfully', 201);
}));

/**
 * @swagger
 * /auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', utils.handleAsyncError(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return utils.sendError(res, 'Refresh token required', 400);
  }

  try {
    const decoded = auth.verifyToken(refreshToken);
    
    // Check if session exists and is active
    const session = await prisma.sessionLog.findUnique({
      where: { token: refreshToken },
      include: {
        user: {
          include: {
            roles: {
              include: {
                role: {
                  include: {
                    permissions: {
                      include: {
                        permission: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!session || !session.isActive || session.expiresAt < new Date()) {
      return utils.sendError(res, 'Invalid or expired refresh token', 401);
    }

    const user = session.user;
    const roles = user.roles.map(ur => ur.role.name);
    const permissions = user.roles.flatMap(ur => 
      ur.role.permissions.map(rp => rp.permission.name)
    );

    // Generate new access token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      username: user.username,
      roles,
      permissions,
    };

    const accessToken = auth.generateAccessToken(tokenPayload);

    utils.sendSuccess(res, { accessToken }, 'Token refreshed successfully');
  } catch (error) {
    return utils.sendError(res, 'Invalid refresh token', 401);
  }
}));

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: User logout
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post('/logout', auth.authenticateToken, utils.handleAsyncError(async (req, res) => {
  const { refreshToken } = req.body;

  if (refreshToken) {
    // Deactivate the session
    await prisma.sessionLog.updateMany({
      where: { token: refreshToken },
      data: { isActive: false }
    });
  }

  utils.sendSuccess(res, null, 'Logout successful');
}));

/**
 * @swagger
 * /auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/me', auth.authenticateToken, utils.handleAsyncError(async (req, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user.userId },
    include: {
      roles: {
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      }
    }
  });

  if (!user) {
    return utils.sendError(res, 'User not found', 404);
  }

  const roles = user.roles.map(ur => ur.role.name);
  const permissions = user.roles.flatMap(ur => 
    ur.role.permissions.map(rp => rp.permission.name)
  );

  // Remove password from response
  const { password: _, ...userResponse } = user;

  utils.sendSuccess(res, {
    ...userResponse,
    roles,
    permissions,
  }, 'User profile retrieved successfully');
}));

module.exports = router;
