import nodemailer from 'nodemailer';
import webpush from 'web-push';
import { EmailOptions } from '@shop/shared';

// Email configuration
const emailTransporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST || 'localhost',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

// Web Push configuration
webpush.setVapidDetails(
  process.env.VAPID_SUBJECT || 'mailto:<EMAIL>',
  process.env.VAPID_PUBLIC_KEY || '',
  process.env.VAPID_PRIVATE_KEY || ''
);

export const sendEmail = async (options: EmailOptions): Promise<void> => {
  try {
    await emailTransporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
      attachments: options.attachments,
    });
    console.log(`Email sent to ${options.to}`);
  } catch (error) {
    console.error('Email send error:', error);
    throw error;
  }
};

export const sendPushNotification = async (
  userId: string,
  payload: {
    title: string;
    body: string;
    data?: Record<string, any>;
  }
): Promise<void> => {
  try {
    // In a real implementation, you would:
    // 1. Get user's push subscription from database
    // 2. Send push notification using webpush.sendNotification()
    
    // For now, just log the notification
    console.log(`Push notification for user ${userId}:`, payload);
    
    // Example implementation:
    // const subscription = await getUserPushSubscription(userId);
    // if (subscription) {
    //   await webpush.sendNotification(subscription, JSON.stringify(payload));
    // }
  } catch (error) {
    console.error('Push notification error:', error);
    throw error;
  }
};

export const sendSMS = async (
  phoneNumber: string,
  message: string
): Promise<void> => {
  try {
    // In a real implementation, you would integrate with SMS service like Twilio
    console.log(`SMS to ${phoneNumber}: ${message}`);
  } catch (error) {
    console.error('SMS send error:', error);
    throw error;
  }
};

export default {
  sendEmail,
  sendPushNotification,
  sendSMS,
};
